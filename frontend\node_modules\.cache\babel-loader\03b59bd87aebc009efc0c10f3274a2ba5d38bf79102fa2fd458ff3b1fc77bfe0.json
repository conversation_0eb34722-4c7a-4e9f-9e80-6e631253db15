{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\components\\\\Modal.js\",\n  _s = $RefreshSig$();\n// Modal.js\n// Modern modal component with VectorShift styling\n\nimport { useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Modal = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  showCloseButton = true\n}) => {\n  _s();\n  // Handle escape key\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-backdrop\",\n    onClick: handleBackdropClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), showCloseButton && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onClose,\n          \"aria-label\": \"Close modal\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Modal, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Modal;\nexport const PipelineAnalysisModal = ({\n  isOpen,\n  onClose,\n  analysisResult\n}) => {\n  if (!analysisResult) return null;\n  const {\n    num_nodes,\n    num_edges,\n    is_dag\n  } = analysisResult;\n  const dagStatus = is_dag ? \"✅ Valid DAG\" : \"❌ Contains Cycles\";\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: isOpen,\n    onClose: onClose,\n    title: /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"\\uD83D\\uDCCA Pipeline Analysis Results\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-stat\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"modal-stat-icon\",\n          children: \"\\uD83D\\uDCE6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"modal-stat-text\",\n          children: [\"Number of Nodes: \", num_nodes]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-stat\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"modal-stat-icon\",\n          children: \"\\uD83D\\uDD17\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"modal-stat-text\",\n          children: [\"Number of Edges: \", num_edges]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-stat\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"modal-stat-icon\",\n          children: \"\\uD83D\\uDD04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"modal-stat-text\",\n          children: [\"DAG Status: \", dagStatus]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `modal-status ${is_dag ? 'success' : 'error'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: is_dag ? '✅' : '⚠️'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: is_dag ? \"Great! Your pipeline is a valid Directed Acyclic Graph.\" : \"Warning: Your pipeline contains cycles and is not a valid DAG.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-actions\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"modal-button primary\",\n        onClick: onClose,\n        children: \"Got it!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_c2 = PipelineAnalysisModal;\nvar _c, _c2;\n$RefreshReg$(_c, \"Modal\");\n$RefreshReg$(_c2, \"PipelineAnalysisModal\");", "map": {"version": 3, "names": ["useEffect", "jsxDEV", "_jsxDEV", "Modal", "isOpen", "onClose", "title", "children", "showCloseButton", "_s", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "handleBackdropClick", "target", "currentTarget", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "PipelineAnalysisModal", "analysisResult", "num_nodes", "num_edges", "is_dag", "dagStatus", "_c2", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/components/Modal.js"], "sourcesContent": ["// Modal.js\n// Modern modal component with VectorShift styling\n\nimport { useEffect } from 'react';\n\nexport const Modal = ({ \n  isOpen, \n  onClose, \n  title, \n  children, \n  showCloseButton = true \n}) => {\n  // Handle escape key\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div className=\"modal-backdrop\" onClick={handleBackdropClick}>\n      <div className=\"modal-container\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">{title}</h2>\n          {showCloseButton && (\n            <button \n              className=\"modal-close\" \n              onClick={onClose}\n              aria-label=\"Close modal\"\n            >\n              ×\n            </button>\n          )}\n        </div>\n        <div className=\"modal-content\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport const PipelineAnalysisModal = ({ \n  isOpen, \n  onClose, \n  analysisResult \n}) => {\n  if (!analysisResult) return null;\n\n  const { num_nodes, num_edges, is_dag } = analysisResult;\n  const dagStatus = is_dag ? \"✅ Valid DAG\" : \"❌ Contains Cycles\";\n\n  return (\n    <Modal \n      isOpen={isOpen} \n      onClose={onClose} \n      title={\n        <span>\n          📊 Pipeline Analysis Results\n        </span>\n      }\n    >\n      <div className=\"modal-section\">\n        <div className=\"modal-stat\">\n          <span className=\"modal-stat-icon\">📦</span>\n          <span className=\"modal-stat-text\">Number of Nodes: {num_nodes}</span>\n        </div>\n        \n        <div className=\"modal-stat\">\n          <span className=\"modal-stat-icon\">🔗</span>\n          <span className=\"modal-stat-text\">Number of Edges: {num_edges}</span>\n        </div>\n        \n        <div className=\"modal-stat\">\n          <span className=\"modal-stat-icon\">🔄</span>\n          <span className=\"modal-stat-text\">DAG Status: {dagStatus}</span>\n        </div>\n      </div>\n\n      <div className={`modal-status ${is_dag ? 'success' : 'error'}`}>\n        <span>{is_dag ? '✅' : '⚠️'}</span>\n        <span>\n          {is_dag \n            ? \"Great! Your pipeline is a valid Directed Acyclic Graph.\" \n            : \"Warning: Your pipeline contains cycles and is not a valid DAG.\"\n          }\n        </span>\n      </div>\n\n      <div className=\"modal-actions\">\n        <button \n          className=\"modal-button primary\" \n          onClick={onClose}\n        >\n          Got it!\n        </button>\n      </div>\n    </Modal>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,OAAO,MAAMC,KAAK,GAAGA,CAAC;EACpBC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAC,EAAA;EACJ;EACAT,SAAS,CAAC,MAAM;IACd,MAAMU,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,IAAIR,MAAM,EAAE;QAChCC,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVS,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACb,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMe,mBAAmB,GAAIR,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACS,MAAM,KAAKT,CAAC,CAACU,aAAa,EAAE;MAChChB,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEH,OAAA;IAAKoB,SAAS,EAAC,gBAAgB;IAACC,OAAO,EAAEJ,mBAAoB;IAAAZ,QAAA,eAC3DL,OAAA;MAAKoB,SAAS,EAAC,iBAAiB;MAAAf,QAAA,gBAC9BL,OAAA;QAAKoB,SAAS,EAAC,cAAc;QAAAf,QAAA,gBAC3BL,OAAA;UAAIoB,SAAS,EAAC,aAAa;UAAAf,QAAA,EAAED;QAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACvCnB,eAAe,iBACdN,OAAA;UACEoB,SAAS,EAAC,aAAa;UACvBC,OAAO,EAAElB,OAAQ;UACjB,cAAW,aAAa;UAAAE,QAAA,EACzB;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,eAAe;QAAAf,QAAA,EAC3BA;MAAQ;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CAvDWN,KAAK;AAAAyB,EAAA,GAALzB,KAAK;AAyDlB,OAAO,MAAM0B,qBAAqB,GAAGA,CAAC;EACpCzB,MAAM;EACNC,OAAO;EACPyB;AACF,CAAC,KAAK;EACJ,IAAI,CAACA,cAAc,EAAE,OAAO,IAAI;EAEhC,MAAM;IAAEC,SAAS;IAAEC,SAAS;IAAEC;EAAO,CAAC,GAAGH,cAAc;EACvD,MAAMI,SAAS,GAAGD,MAAM,GAAG,aAAa,GAAG,mBAAmB;EAE9D,oBACE/B,OAAA,CAACC,KAAK;IACJC,MAAM,EAAEA,MAAO;IACfC,OAAO,EAAEA,OAAQ;IACjBC,KAAK,eACHJ,OAAA;MAAAK,QAAA,EAAM;IAEN;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACP;IAAApB,QAAA,gBAEDL,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAf,QAAA,gBAC5BL,OAAA;QAAKoB,SAAS,EAAC,YAAY;QAAAf,QAAA,gBACzBL,OAAA;UAAMoB,SAAS,EAAC,iBAAiB;UAAAf,QAAA,EAAC;QAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3CzB,OAAA;UAAMoB,SAAS,EAAC,iBAAiB;UAAAf,QAAA,GAAC,mBAAiB,EAACwB,SAAS;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,YAAY;QAAAf,QAAA,gBACzBL,OAAA;UAAMoB,SAAS,EAAC,iBAAiB;UAAAf,QAAA,EAAC;QAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3CzB,OAAA;UAAMoB,SAAS,EAAC,iBAAiB;UAAAf,QAAA,GAAC,mBAAiB,EAACyB,SAAS;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,YAAY;QAAAf,QAAA,gBACzBL,OAAA;UAAMoB,SAAS,EAAC,iBAAiB;UAAAf,QAAA,EAAC;QAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3CzB,OAAA;UAAMoB,SAAS,EAAC,iBAAiB;UAAAf,QAAA,GAAC,cAAY,EAAC2B,SAAS;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzB,OAAA;MAAKoB,SAAS,EAAG,gBAAeW,MAAM,GAAG,SAAS,GAAG,OAAQ,EAAE;MAAA1B,QAAA,gBAC7DL,OAAA;QAAAK,QAAA,EAAO0B,MAAM,GAAG,GAAG,GAAG;MAAI;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClCzB,OAAA;QAAAK,QAAA,EACG0B,MAAM,GACH,yDAAyD,GACzD;MAAgE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENzB,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAf,QAAA,eAC5BL,OAAA;QACEoB,SAAS,EAAC,sBAAsB;QAChCC,OAAO,EAAElB,OAAQ;QAAAE,QAAA,EAClB;MAED;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACQ,GAAA,GAzDWN,qBAAqB;AAAA,IAAAD,EAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}