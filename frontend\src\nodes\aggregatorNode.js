// aggregatorNode.js
// Demonstrates data aggregation with multiple inputs and operations

import { useState } from 'react';
import { BaseNode, createHandle } from './BaseNode';
import { Position } from 'reactflow';
import { NodeInput } from '../components/NodeInput';

export const AggregatorNode = ({ id, data }) => {
  const [operation, setOperation] = useState(data?.operation || 'concat');
  const [separator, setSeparator] = useState(data?.separator || ', ');

  const handleOperationChange = (e) => {
    setOperation(e.target.value);
  };

  const handleSeparatorChange = (e) => {
    setSeparator(e.target.value);
  };

  const handles = [
    createHandle('input1', 'target', Position.Left, { top: '20%' }),
    createHandle('input2', 'target', Position.Left, { top: '40%' }),
    createHandle('input3', 'target', Position.Left, { top: '60%' }),
    createHandle('input4', 'target', Position.Left, { top: '80%' }),
    createHandle('result', 'source', Position.Right)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Aggregator"
      handles={handles}
      nodeType="aggregator"
      height={120}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
        <label style={{ fontSize: '11px', display: 'flex', flexDirection: 'column', gap: '2px' }}>
          Operation:
          <select
            value={operation}
            onChange={handleOperationChange}
            className="node-input"
            style={{ cursor: 'pointer', fontSize: '10px' }}
          >
            <option value="concat">Concat</option>
            <option value="sum">Sum</option>
            <option value="avg">Average</option>
            <option value="max">Max</option>
            <option value="min">Min</option>
          </select>
        </label>
        {operation === 'concat' && (
          <label style={{ fontSize: '11px', display: 'flex', flexDirection: 'column', gap: '2px' }}>
            Separator:
            <NodeInput
              value={separator}
              onChange={handleSeparatorChange}
              placeholder="Enter separator"
              style={{ fontSize: '10px' }}
            />
          </label>
        )}
        <div style={{ fontSize: '9px', color: 'rgba(255, 255, 255, 0.6)' }}>
          Combines multiple inputs
        </div>
      </div>
    </BaseNode>
  );
};
