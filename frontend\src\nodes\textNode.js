// textNode.js

import { useState, useEffect, useRef } from 'react';
import { BaseNode, HANDLE_CONFIGS, createHandle } from './BaseNode';
import { Position } from 'reactflow';

export const TextNode = ({ id, data }) => {
  const [currText, setCurrText] = useState(data?.text || '{{input}}');
  const [nodeSize, setNodeSize] = useState({ width: 220, height: 120 });
  const [variables, setVariables] = useState([]);
  const textareaRef = useRef(null);

  // Extract variables from text using regex for {{variableName}}
  const extractVariables = (text) => {
    const regex = /\{\{([a-zA-Z_$][a-zA-Z0-9_$]*)\}\}/g;
    const matches = [];
    let match;

    while ((match = regex.exec(text)) !== null) {
      const variableName = match[1];
      if (!matches.includes(variableName)) {
        matches.push(variableName);
      }
    }

    return matches;
  };

  // Calculate dynamic size based on text content
  const calculateSize = (text) => {
    const lines = text.split('\n');
    const maxLineLength = Math.max(...lines.map(line => line.length));

    // Base dimensions
    const baseWidth = 220;
    const baseHeight = 120;

    // Dynamic width based on content
    const charWidth = 8; // approximate character width
    const dynamicWidth = Math.max(baseWidth, Math.min(400, maxLineLength * charWidth + 60));

    // Dynamic height based on lines
    const lineHeight = 20;
    const dynamicHeight = Math.max(baseHeight, lines.length * lineHeight + 80);

    return { width: dynamicWidth, height: dynamicHeight };
  };

  const handleTextChange = (e) => {
    const newText = e.target.value;
    setCurrText(newText);

    // Update node size based on content
    const newSize = calculateSize(newText);
    setNodeSize(newSize);

    // Extract and update variables
    const newVariables = extractVariables(newText);
    setVariables(newVariables);
  };

  // Initialize variables and size on mount
  useEffect(() => {
    const initialVariables = extractVariables(currText);
    setVariables(initialVariables);

    const initialSize = calculateSize(currText);
    setNodeSize(initialSize);
  }, [currText]);

  // Create handles: one output handle + input handles for each variable
  const handles = [
    HANDLE_CONFIGS.sourceRight('output'),
    ...variables.map((variable, index) =>
      createHandle(
        variable,
        'target',
        Position.Left,
        { top: `${30 + (index * 25)}%` }
      )
    )
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Text"
      handles={handles}
      nodeType="text"
      width={nodeSize.width}
      height={nodeSize.height}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', height: '100%' }}>
        <label style={{ fontSize: '12px', display: 'flex', flexDirection: 'column' }}>
          Text:
          <textarea
            ref={textareaRef}
            value={currText}
            onChange={handleTextChange}
            placeholder="Enter text with variables like {{variableName}}"
            style={{
              marginTop: '4px',
              fontSize: '11px',
              width: '100%',
              height: `${nodeSize.height - 80}px`,
              resize: 'none',
              border: '1px solid #d1d5db',
              borderRadius: '4px',
              padding: '8px',
              fontFamily: 'inherit',
              outline: 'none',
              transition: 'border-color 0.2s ease'
            }}
            onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
            onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
          />
        </label>

        {/* Display detected variables */}
        {variables.length > 0 && (
          <div style={{
            fontSize: '10px',
            color: '#6b7280',
            marginTop: '4px',
            padding: '4px',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderRadius: '4px',
            border: '1px solid rgba(59, 130, 246, 0.2)'
          }}>
            <strong>Variables:</strong> {variables.join(', ')}
          </div>
        )}
      </div>
    </BaseNode>
  );
}
