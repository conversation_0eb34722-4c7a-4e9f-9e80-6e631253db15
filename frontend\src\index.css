/* Import VectorShift Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Studio Feixen Sans Alternative - Using Inter as fallback with custom styling */
@font-face {
  font-family: 'Studio Feixen Sans';
  src: local('Inter'), local('Inter-Regular');
  font-weight: normal;
  font-style: normal;
}

/* CSS Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  font-family: 'Studio Feixen Sans', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0F0C1A 0%, #1D1847 50%, #2E2A67 100%);
  min-height: 100vh;
  color: #FFFFFF;
  overflow-x: hidden;
}

code {
  font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* App Layout - Performance Optimized */
.app-container {
  display: flex;
  min-height: 100vh;
  background: #1a1a1a;
  color: #ffffff;
  font-family: 'Inter', sans-serif;
}

/* Sidebar Layout */
.app-sidebar {
  width: 280px;
  background: #2a2a2a;
  border-right: 1px solid #3a3a3a;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 100;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
}

/* Toolbar Styles */
.pipeline-toolbar {
  background: rgba(15, 12, 26, 0.85);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(140, 79, 255, 0.2);
  padding: 24px 32px;
  box-shadow: 0 8px 32px rgba(140, 79, 255, 0.15);
  position: relative;
  z-index: 10;
}

.toolbar-title {
  font-size: 32px;
  font-weight: 900;
  color: #FFFFFF;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 2px;
  background: linear-gradient(135deg, #8C4FFF 0%, #B770FF 50%, #FFFFFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(140, 79, 255, 0.5);
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
}

.toolbar-subtitle {
  font-size: 14px;
  color: #DDDDDD;
  margin-bottom: 24px;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

.toolbar-nodes {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

/* Draggable Node Styles */
.draggable-node {
  cursor: grab;
  min-width: 120px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(140, 79, 255, 0.2) 0%, rgba(183, 112, 255, 0.1) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(140, 79, 255, 0.3);
  color: #FFFFFF;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 16px rgba(140, 79, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  user-select: none;
  position: relative;
  overflow: hidden;
}

.draggable-node::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 500ms ease;
}

.draggable-node:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 16px 32px rgba(140, 79, 255, 0.4),
    0 0 0 1px rgba(140, 79, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(140, 79, 255, 0.6);
  background: linear-gradient(135deg, rgba(140, 79, 255, 0.3) 0%, rgba(183, 112, 255, 0.2) 100%);
}

.draggable-node:hover::before {
  left: 100%;
}

.draggable-node:active {
  cursor: grabbing;
  transform: translateY(-2px) scale(1.02);
  transition: all 150ms ease;
}

/* Pipeline UI Container */
.pipeline-ui-container {
  flex: 1;
  position: relative;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  margin: 24px 32px;
  border-radius: 24px;
  overflow: hidden;
  border: 1px solid rgba(140, 79, 255, 0.2);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(140, 79, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  z-index: 5;
}

.pipeline-ui-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(140, 79, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(183, 112, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* Submit Button Container */
.submit-container {
  padding: 32px;
  background: rgba(15, 12, 26, 0.85);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(140, 79, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 10;
}

.submit-button {
  background: linear-gradient(135deg, #8C4FFF 0%, #B770FF 100%);
  color: #FFFFFF;
  border: none;
  padding: 16px 48px;
  border-radius: 16px;
  font-weight: 700;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  cursor: pointer;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 24px rgba(140, 79, 255, 0.4),
    0 0 0 1px rgba(140, 79, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 500ms ease;
}

.submit-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 16px 40px rgba(140, 79, 255, 0.6),
    0 0 0 1px rgba(140, 79, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg, #9D5FFF 0%, #C880FF 100%);
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:active {
  transform: translateY(0) scale(1);
  transition: all 150ms ease;
}

/* Pulse animation for submit button */
@keyframes pulse {
  0% { box-shadow: 0 8px 24px rgba(140, 79, 255, 0.4), 0 0 0 0 rgba(140, 79, 255, 0.7); }
  70% { box-shadow: 0 8px 24px rgba(140, 79, 255, 0.4), 0 0 0 10px rgba(140, 79, 255, 0); }
  100% { box-shadow: 0 8px 24px rgba(140, 79, 255, 0.4), 0 0 0 0 rgba(140, 79, 255, 0); }
}

.submit-button:focus {
  animation: pulse 2s infinite;
  outline: none;
}

/* ReactFlow Custom Styling */
.react-flow__controls {
  background: rgba(15, 12, 26, 0.9) !important;
  border: 1px solid rgba(140, 79, 255, 0.3) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 16px rgba(140, 79, 255, 0.2) !important;
}

.react-flow__controls-button {
  background: transparent !important;
  border: none !important;
  color: #FFFFFF !important;
  transition: all 200ms ease !important;
}

.react-flow__controls-button:hover {
  background: rgba(140, 79, 255, 0.2) !important;
  color: #B770FF !important;
}

.react-flow__minimap {
  background: rgba(15, 12, 26, 0.9) !important;
  border: 1px solid rgba(140, 79, 255, 0.3) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 16px rgba(140, 79, 255, 0.2) !important;
}

.react-flow__edge-path {
  stroke: #8C4FFF !important;
  stroke-width: 2px !important;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #B770FF !important;
  stroke-width: 3px !important;
}

.react-flow__connection-line {
  stroke: #8C4FFF !important;
  stroke-width: 2px !important;
}

/* Custom scrollbar for better aesthetics */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 12, 26, 0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #8C4FFF, #B770FF);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #9D5FFF, #C880FF);
}
