{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\toolbar.js\";\n// toolbar.js\n\nimport { DraggableNode } from './draggableNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PipelineToolbar = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pipeline-toolbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-title\",\n      children: \"VectorShift Pipeline Builder\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-subtitle\",\n      children: \"Drag nodes to canvas \\u2022 Connect workflows \\u2022 Build the future\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-nodes\",\n      children: [/*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"customInput\",\n        label: \"Input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"llm\",\n        label: \"LLM\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"customOutput\",\n        label: \"Output\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"text\",\n        label: \"Text\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"math\",\n        label: \"Math\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"filter\",\n        label: \"Filter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"timer\",\n        label: \"Timer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"switch\",\n        label: \"Switch\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n        type: \"aggregator\",\n        label: \"Aggregator\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n_c = PipelineToolbar;\nvar _c;\n$RefreshReg$(_c, \"PipelineToolbar\");", "map": {"version": 3, "names": ["DraggableNode", "jsxDEV", "_jsxDEV", "PipelineToolbar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "label", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/toolbar.js"], "sourcesContent": ["// toolbar.js\n\nimport { DraggableNode } from './draggableNode';\n\nexport const PipelineToolbar = () => {\n\n    return (\n        <div className=\"pipeline-toolbar\">\n            <div className=\"toolbar-title\">\n                VectorShift Pipeline Builder\n            </div>\n            <div className=\"toolbar-subtitle\">\n                Drag nodes to canvas • Connect workflows • Build the future\n            </div>\n            <div className=\"toolbar-nodes\">\n                {/* Core Nodes */}\n                <DraggableNode type='customInput' label='Input' />\n                <DraggableNode type='llm' label='LLM' />\n                <DraggableNode type='customOutput' label='Output' />\n                <DraggableNode type='text' label='Text' />\n\n                {/* Advanced Nodes */}\n                <DraggableNode type='math' label='Math' />\n                <DraggableNode type='filter' label='Filter' />\n                <DraggableNode type='timer' label='Timer' />\n                <DraggableNode type='switch' label='Switch' />\n                <DraggableNode type='aggregator' label='Aggregator' />\n            </div>\n        </div>\n    );\n};\n"], "mappings": ";AAAA;;AAEA,SAASA,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAEjC,oBACID,OAAA;IAAKE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC7BH,OAAA;MAAKE,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAE/B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAElC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE1BH,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,aAAa;QAACC,KAAK,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDP,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,KAAK;QAACC,KAAK,EAAC;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxCP,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,cAAc;QAACC,KAAK,EAAC;MAAQ;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDP,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAAM;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1CP,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAAM;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CP,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAC;MAAQ;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CP,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,OAAO;QAACC,KAAK,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5CP,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAC;MAAQ;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CP,OAAA,CAACF,aAAa;QAACU,IAAI,EAAC,YAAY;QAACC,KAAK,EAAC;MAAY;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACG,EAAA,GA1BWT,eAAe;AAAA,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}