{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\toolbar.js\";\n// toolbar.js\n\nimport { DraggableNode } from './draggableNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PipelineToolbar = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pipeline-toolbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-brand\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"brand-icon\",\n          children: \"\\u26A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"brand-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"toolbar-title\",\n            children: \"VectorShift Pipeline Builder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"toolbar-subtitle\",\n            children: \"Leverage AI across data of all formats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-title\",\n        children: \"\\uD83D\\uDCC4 Data Sources\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-nodes\",\n        children: [/*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"customInput\",\n          label: \"Document\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"text\",\n          label: \"Text\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-title\",\n        children: \"\\uD83E\\uDD16 AI Processing\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-nodes\",\n        children: [/*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"llm\",\n          label: \"AI Model\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"filter\",\n          label: \"Filter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"aggregator\",\n          label: \"Aggregator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-title\",\n        children: \"\\u2699\\uFE0F Logic & Output\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-nodes\",\n        children: [/*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"math\",\n          label: \"Math\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"switch\",\n          label: \"Switch\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"timer\",\n          label: \"Timer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"customOutput\",\n          label: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n_c = PipelineToolbar;\nvar _c;\n$RefreshReg$(_c, \"PipelineToolbar\");", "map": {"version": 3, "names": ["DraggableNode", "jsxDEV", "_jsxDEV", "PipelineToolbar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "label", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/toolbar.js"], "sourcesContent": ["// toolbar.js\n\nimport { DraggableNode } from './draggableNode';\n\nexport const PipelineToolbar = () => {\n\n    return (\n        <div className=\"pipeline-toolbar\">\n            <div className=\"toolbar-header\">\n                <div className=\"toolbar-brand\">\n                    <div className=\"brand-icon\">⚡</div>\n                    <div className=\"brand-text\">\n                        <div className=\"toolbar-title\">\n                            VectorShift Pipeline Builder\n                        </div>\n                        <div className=\"toolbar-subtitle\">\n                            Leverage AI across data of all formats\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"toolbar-section\">\n                <div className=\"section-title\">📄 Data Sources</div>\n                <div className=\"toolbar-nodes\">\n                    <DraggableNode type='customInput' label='Document' />\n                    <DraggableNode type='text' label='Text' />\n                </div>\n            </div>\n\n            <div className=\"toolbar-section\">\n                <div className=\"section-title\">🤖 AI Processing</div>\n                <div className=\"toolbar-nodes\">\n                    <DraggableNode type='llm' label='AI Model' />\n                    <DraggableNode type='filter' label='Filter' />\n                    <DraggableNode type='aggregator' label='Aggregator' />\n                </div>\n            </div>\n\n            <div className=\"toolbar-section\">\n                <div className=\"section-title\">⚙️ Logic & Output</div>\n                <div className=\"toolbar-nodes\">\n                    <DraggableNode type='math' label='Math' />\n                    <DraggableNode type='switch' label='Switch' />\n                    <DraggableNode type='timer' label='Timer' />\n                    <DraggableNode type='customOutput' label='Output' />\n                </div>\n            </div>\n        </div>\n    );\n};\n"], "mappings": ";AAAA;;AAEA,SAASA,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAEjC,oBACID,OAAA;IAAKE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC7BH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BH,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCP,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBH,OAAA;YAAKE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpDP,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BH,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,aAAa;UAACC,KAAK,EAAC;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDP,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC;QAAM;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrDP,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BH,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,KAAK;UAACC,KAAK,EAAC;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CP,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC;QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CP,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,YAAY;UAACC,KAAK,EAAC;QAAY;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtDP,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BH,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC;QAAM;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CP,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC;QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CP,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,OAAO;UAACC,KAAK,EAAC;QAAO;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CP,OAAA,CAACF,aAAa;UAACU,IAAI,EAAC,cAAc;UAACC,KAAK,EAAC;QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACG,EAAA,GA9CWT,eAAe;AAAA,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}