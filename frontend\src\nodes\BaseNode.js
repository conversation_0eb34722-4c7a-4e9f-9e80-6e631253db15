// BaseNode.js
// Reusable base component for all node types

import { Handle, Position } from 'reactflow';
import { theme } from '../styles/theme';

export const BaseNode = ({
  id,
  data,
  title,
  children,
  handles = [],
  style = {},
  className = "",
  width = 220,
  height = 100,
  nodeType = 'default'
}) => {


  const defaultStyle = {
    width,
    height,
    background: 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)',
    border: '1px solid rgba(138, 43, 226, 0.6)',
    borderRadius: '16px',
    padding: theme.spacing.md,
    display: 'flex',
    flexDirection: 'column',
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    fontFamily: "'Studio Feixen Sans', 'Inter', sans-serif",
    position: 'relative',
    color: '#ffffff',
    backdropFilter: 'blur(20px)',
    boxShadow: '0 8px 32px rgba(138, 43, 226, 0.3)',
    ...style
  };

  const titleStyle = {
    fontWeight: '600',
    fontSize: '12px',
    color: '#ffffff',
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
    borderBottom: '1px solid rgba(138, 43, 226, 0.3)',
    paddingBottom: theme.spacing.xs,
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
  };

  const contentStyle = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    fontSize: theme.typography.fontSize.xs,
  };

  const handleStyle = {
    width: '14px',
    height: '14px',
    background: 'linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)',
    border: '2px solid #ffffff',
    borderRadius: '50%',
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    boxShadow: '0 2px 8px rgba(138, 43, 226, 0.4)',
  };

  return (
    <div
      style={defaultStyle}
      className={`base-node ${className}`}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';
        e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.8)';
        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)';
        e.currentTarget.style.boxShadow = '0 12px 40px rgba(138, 43, 226, 0.3)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0) scale(1)';
        e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.4)';
        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.9) 0%, rgba(22, 33, 62, 0.9) 100%)';
        e.currentTarget.style.boxShadow = '0 8px 32px rgba(138, 43, 226, 0.2)';
      }}
    >
      {/* Render handles */}
      {handles.map((handle, index) => (
        <Handle
          key={`${id}-${handle.id || index}`}
          type={handle.type}
          position={handle.position}
          id={`${id}-${handle.id}`}
          style={{
            ...handleStyle,
            ...handle.style
          }}
        />
      ))}

      {/* Title */}
      {title && (
        <div style={titleStyle}>
          <span>{title}</span>
        </div>
      )}

      {/* Content */}
      <div style={contentStyle}>
        {children}
      </div>
    </div>
  );
};

// Helper function to create handle configurations
export const createHandle = (id, type, position, style = {}) => ({
  id,
  type,
  position,
  style
});

// Common handle configurations
export const HANDLE_CONFIGS = {
  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),
  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),
  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),
  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom),
};
