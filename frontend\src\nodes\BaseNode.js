// BaseNode.js
// Reusable base component for all node types

import { Handle, Position } from 'reactflow';
import { theme } from '../styles/theme';

export const BaseNode = ({
  id,
  data,
  title,
  children,
  handles = [],
  style = {},
  className = "",
  width = 220,
  height = 100,
  nodeType = 'default'
}) => {
  const getNodeTypeColor = (type) => {
    return theme.colors.nodeTypes[type] || theme.colors.gray[50];
  };

  const defaultStyle = {
    width,
    height,
    background: `linear-gradient(135deg, ${getNodeTypeColor(nodeType)} 0%, ${getNodeTypeColor(nodeType)}dd 100%)`,
    border: `2px solid ${theme.colors.gray[200]}`,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    display: 'flex',
    flexDirection: 'column',
    boxShadow: theme.shadows.md,
    transition: theme.transitions.normal,
    fontFamily: theme.typography.fontFamily.sans,
    position: 'relative',
    overflow: 'hidden',
    ...style
  };

  const titleStyle = {
    fontWeight: theme.typography.fontWeight.semibold,
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.gray[700],
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
    borderBottom: `1px solid ${theme.colors.gray[200]}`,
    paddingBottom: theme.spacing.xs,
  };

  const contentStyle = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    fontSize: theme.typography.fontSize.xs,
  };

  const handleStyle = {
    width: '12px',
    height: '12px',
    background: theme.colors.primary[500],
    border: `2px solid ${theme.colors.white}`,
    borderRadius: '50%',
    transition: theme.transitions.fast,
  };

  return (
    <div
      style={defaultStyle}
      className={`base-node ${className}`}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-2px)';
        e.currentTarget.style.boxShadow = theme.shadows.lg;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = theme.shadows.md;
      }}
    >
      {/* Render handles */}
      {handles.map((handle, index) => (
        <Handle
          key={`${id}-${handle.id || index}`}
          type={handle.type}
          position={handle.position}
          id={`${id}-${handle.id}`}
          style={{
            ...handleStyle,
            ...handle.style
          }}
        />
      ))}

      {/* Title */}
      {title && (
        <div style={titleStyle}>
          <span>{title}</span>
        </div>
      )}

      {/* Content */}
      <div style={contentStyle}>
        {children}
      </div>
    </div>
  );
};

// Helper function to create handle configurations
export const createHandle = (id, type, position, style = {}) => ({
  id,
  type,
  position,
  style
});

// Common handle configurations
export const HANDLE_CONFIGS = {
  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),
  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),
  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),
  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom),
};
