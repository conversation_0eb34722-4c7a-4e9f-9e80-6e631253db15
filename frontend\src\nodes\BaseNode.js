// BaseNode.js
// Reusable base component for all node types

import { Hand<PERSON>, Position } from 'reactflow';
import { theme } from '../styles/theme';

export const BaseNode = ({
  id,
  data,
  title,
  children,
  handles = [],
  style = {},
  className = "",
  width = 220,
  height = 100,
  nodeType = 'default'
}) => {
  const getNodeTypeColor = (type) => {
    return theme.colors.nodeTypes[type] || theme.colors.gray[50];
  };

  const defaultStyle = {
    width,
    height,
    background: `linear-gradient(135deg, rgba(140, 79, 255, 0.15) 0%, rgba(183, 112, 255, 0.1) 100%)`,
    backdropFilter: 'blur(10px)',
    border: `2px solid rgba(140, 79, 255, 0.3)`,
    borderRadius: '16px',
    padding: theme.spacing.md,
    display: 'flex',
    flexDirection: 'column',
    boxShadow: `
      0 8px 16px rgba(140, 79, 255, 0.2),
      0 0 0 1px rgba(140, 79, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1)
    `,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    fontFamily: "'Studio Feixen Sans', 'Inter', sans-serif",
    position: 'relative',
    overflow: 'hidden',
    color: '#FFFFFF',
    ...style
  };

  const titleStyle = {
    fontWeight: '700',
    fontSize: '14px',
    color: '#FFFFFF',
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
    borderBottom: `1px solid rgba(140, 79, 255, 0.3)`,
    paddingBottom: theme.spacing.xs,
    textTransform: 'uppercase',
    letterSpacing: '1px',
  };

  const contentStyle = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    fontSize: theme.typography.fontSize.xs,
  };

  const handleStyle = {
    width: '14px',
    height: '14px',
    background: 'linear-gradient(135deg, #8C4FFF 0%, #B770FF 100%)',
    border: `2px solid rgba(255, 255, 255, 0.8)`,
    borderRadius: '50%',
    transition: 'all 200ms ease',
    boxShadow: '0 0 8px rgba(140, 79, 255, 0.6)',
  };

  return (
    <div
      style={defaultStyle}
      className={`base-node ${className}`}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';
        e.currentTarget.style.boxShadow = `
          0 16px 32px rgba(140, 79, 255, 0.3),
          0 0 0 1px rgba(140, 79, 255, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.2)
        `;
        e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.5)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0) scale(1)';
        e.currentTarget.style.boxShadow = `
          0 8px 16px rgba(140, 79, 255, 0.2),
          0 0 0 1px rgba(140, 79, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.1)
        `;
        e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.3)';
      }}
    >
      {/* Render handles */}
      {handles.map((handle, index) => (
        <Handle
          key={`${id}-${handle.id || index}`}
          type={handle.type}
          position={handle.position}
          id={`${id}-${handle.id}`}
          style={{
            ...handleStyle,
            ...handle.style
          }}
        />
      ))}

      {/* Title */}
      {title && (
        <div style={titleStyle}>
          <span>{title}</span>
        </div>
      )}

      {/* Content */}
      <div style={contentStyle}>
        {children}
      </div>
    </div>
  );
};

// Helper function to create handle configurations
export const createHandle = (id, type, position, style = {}) => ({
  id,
  type,
  position,
  style
});

// Common handle configurations
export const HANDLE_CONFIGS = {
  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),
  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),
  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),
  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom),
};
