{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\textNode.js\",\n  _s = $RefreshSig$();\n// textNode.js\n\nimport { useState, useEffect, useRef } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TextNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [currText, setCurrText] = useState((data === null || data === void 0 ? void 0 : data.text) || '{{input}}');\n  const [nodeSize, setNodeSize] = useState({\n    width: 220,\n    height: 120\n  });\n  const [variables, setVariables] = useState([]);\n  const textareaRef = useRef(null);\n\n  // Extract variables from text using regex for {{variableName}}\n  const extractVariables = text => {\n    const regex = /\\{\\{([a-zA-Z_$][a-zA-Z0-9_$]*)\\}\\}/g;\n    const matches = [];\n    let match;\n    while ((match = regex.exec(text)) !== null) {\n      const variableName = match[1];\n      if (!matches.includes(variableName)) {\n        matches.push(variableName);\n      }\n    }\n    return matches;\n  };\n\n  // Calculate dynamic size based on text content\n  const calculateSize = text => {\n    const lines = text.split('\\n');\n    const maxLineLength = Math.max(...lines.map(line => line.length));\n\n    // Base dimensions\n    const baseWidth = 220;\n    const baseHeight = 120;\n\n    // Dynamic width based on content\n    const charWidth = 8; // approximate character width\n    const dynamicWidth = Math.max(baseWidth, Math.min(400, maxLineLength * charWidth + 60));\n\n    // Dynamic height based on lines\n    const lineHeight = 20;\n    const dynamicHeight = Math.max(baseHeight, lines.length * lineHeight + 80);\n    return {\n      width: dynamicWidth,\n      height: dynamicHeight\n    };\n  };\n  const handleTextChange = e => {\n    const newText = e.target.value;\n    setCurrText(newText);\n\n    // Update node size based on content\n    const newSize = calculateSize(newText);\n    setNodeSize(newSize);\n\n    // Extract and update variables\n    const newVariables = extractVariables(newText);\n    setVariables(newVariables);\n  };\n\n  // Initialize variables and size on mount\n  useEffect(() => {\n    const initialVariables = extractVariables(currText);\n    setVariables(initialVariables);\n    const initialSize = calculateSize(currText);\n    setNodeSize(initialSize);\n  }, [currText]);\n\n  // Create handles: one output handle + input handles for each variable\n  const handles = [HANDLE_CONFIGS.sourceRight('output'), ...variables.map((variable, index) => createHandle(variable, 'target', Position.Left, {\n    top: `${30 + index * 25}%`\n  }))];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Text\",\n    handles: handles,\n    nodeType: \"text\",\n    width: nodeSize.width,\n    height: nodeSize.height,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '4px',\n        height: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          color: '#ffffff',\n          fontWeight: '600'\n        },\n        children: [\"Text:\", /*#__PURE__*/_jsxDEV(\"textarea\", {\n          ref: textareaRef,\n          value: currText,\n          onChange: handleTextChange,\n          placeholder: \"Enter text with variables like {{variableName}}\",\n          style: {\n            marginTop: '4px',\n            fontSize: '11px',\n            width: '100%',\n            height: `${nodeSize.height - 80}px`,\n            resize: 'none',\n            border: '1px solid rgba(138, 43, 226, 0.3)',\n            borderRadius: '6px',\n            padding: '8px',\n            fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n            outline: 'none',\n            transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n            backgroundColor: 'rgba(26, 11, 46, 0.8)',\n            color: '#ffffff',\n            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n          },\n          onFocus: e => {\n            e.target.style.borderColor = 'rgba(138, 43, 226, 0.8)';\n            e.target.style.boxShadow = '0 0 0 2px rgba(138, 43, 226, 0.2)';\n            e.target.style.backgroundColor = 'rgba(26, 11, 46, 0.9)';\n          },\n          onBlur: e => {\n            e.target.style.borderColor = 'rgba(138, 43, 226, 0.3)';\n            e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';\n            e.target.style.backgroundColor = 'rgba(26, 11, 46, 0.8)';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), variables.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: '#ffffff',\n          marginTop: '4px',\n          padding: '6px',\n          backgroundColor: 'rgba(138, 43, 226, 0.2)',\n          borderRadius: '6px',\n          border: '1px solid rgba(138, 43, 226, 0.4)',\n          fontWeight: '500'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Variables:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), \" \", variables.join(', ')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(TextNode, \"myPsVDdYfNZ15WrOOFXkmbHooHM=\");\n_c = TextNode;\nvar _c;\n$RefreshReg$(_c, \"TextNode\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "BaseNode", "HANDLE_CONFIGS", "createHandle", "Position", "jsxDEV", "_jsxDEV", "TextNode", "id", "data", "_s", "currText", "setCurrText", "text", "nodeSize", "setNodeSize", "width", "height", "variables", "setVariables", "textareaRef", "extractVariables", "regex", "matches", "match", "exec", "variableName", "includes", "push", "calculateSize", "lines", "split", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "max", "map", "line", "length", "baseWidth", "baseHeight", "char<PERSON><PERSON><PERSON>", "dynamicWidth", "min", "lineHeight", "dynamicHeight", "handleTextChange", "e", "newText", "target", "value", "newSize", "newVariables", "initialVariables", "initialSize", "handles", "sourceRight", "variable", "index", "Left", "top", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "fontSize", "color", "fontWeight", "ref", "onChange", "placeholder", "marginTop", "resize", "border", "borderRadius", "padding", "fontFamily", "outline", "transition", "backgroundColor", "boxShadow", "onFocus", "borderColor", "onBlur", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "join", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/textNode.js"], "sourcesContent": ["// textNode.js\n\nimport { useState, useEffect, useRef } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\n\nexport const TextNode = ({ id, data }) => {\n  const [currText, setCurrText] = useState(data?.text || '{{input}}');\n  const [nodeSize, setNodeSize] = useState({ width: 220, height: 120 });\n  const [variables, setVariables] = useState([]);\n  const textareaRef = useRef(null);\n\n  // Extract variables from text using regex for {{variableName}}\n  const extractVariables = (text) => {\n    const regex = /\\{\\{([a-zA-Z_$][a-zA-Z0-9_$]*)\\}\\}/g;\n    const matches = [];\n    let match;\n\n    while ((match = regex.exec(text)) !== null) {\n      const variableName = match[1];\n      if (!matches.includes(variableName)) {\n        matches.push(variableName);\n      }\n    }\n\n    return matches;\n  };\n\n  // Calculate dynamic size based on text content\n  const calculateSize = (text) => {\n    const lines = text.split('\\n');\n    const maxLineLength = Math.max(...lines.map(line => line.length));\n\n    // Base dimensions\n    const baseWidth = 220;\n    const baseHeight = 120;\n\n    // Dynamic width based on content\n    const charWidth = 8; // approximate character width\n    const dynamicWidth = Math.max(baseWidth, Math.min(400, maxLineLength * charWidth + 60));\n\n    // Dynamic height based on lines\n    const lineHeight = 20;\n    const dynamicHeight = Math.max(baseHeight, lines.length * lineHeight + 80);\n\n    return { width: dynamicWidth, height: dynamicHeight };\n  };\n\n  const handleTextChange = (e) => {\n    const newText = e.target.value;\n    setCurrText(newText);\n\n    // Update node size based on content\n    const newSize = calculateSize(newText);\n    setNodeSize(newSize);\n\n    // Extract and update variables\n    const newVariables = extractVariables(newText);\n    setVariables(newVariables);\n  };\n\n  // Initialize variables and size on mount\n  useEffect(() => {\n    const initialVariables = extractVariables(currText);\n    setVariables(initialVariables);\n\n    const initialSize = calculateSize(currText);\n    setNodeSize(initialSize);\n  }, [currText]);\n\n  // Create handles: one output handle + input handles for each variable\n  const handles = [\n    HANDLE_CONFIGS.sourceRight('output'),\n    ...variables.map((variable, index) =>\n      createHandle(\n        variable,\n        'target',\n        Position.Left,\n        { top: `${30 + (index * 25)}%` }\n      )\n    )\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Text\"\n      handles={handles}\n      nodeType=\"text\"\n      width={nodeSize.width}\n      height={nodeSize.height}\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', height: '100%' }}>\n        <label style={{\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          color: '#ffffff',\n          fontWeight: '600'\n        }}>\n          Text:\n          <textarea\n            ref={textareaRef}\n            value={currText}\n            onChange={handleTextChange}\n            placeholder=\"Enter text with variables like {{variableName}}\"\n            style={{\n              marginTop: '4px',\n              fontSize: '11px',\n              width: '100%',\n              height: `${nodeSize.height - 80}px`,\n              resize: 'none',\n              border: '1px solid rgba(138, 43, 226, 0.3)',\n              borderRadius: '6px',\n              padding: '8px',\n              fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n              outline: 'none',\n              transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n              backgroundColor: 'rgba(26, 11, 46, 0.8)',\n              color: '#ffffff',\n              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n            }}\n            onFocus={(e) => {\n              e.target.style.borderColor = 'rgba(138, 43, 226, 0.8)';\n              e.target.style.boxShadow = '0 0 0 2px rgba(138, 43, 226, 0.2)';\n              e.target.style.backgroundColor = 'rgba(26, 11, 46, 0.9)';\n            }}\n            onBlur={(e) => {\n              e.target.style.borderColor = 'rgba(138, 43, 226, 0.3)';\n              e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';\n              e.target.style.backgroundColor = 'rgba(26, 11, 46, 0.8)';\n            }}\n          />\n        </label>\n\n        {/* Display detected variables */}\n        {variables.length > 0 && (\n          <div style={{\n            fontSize: '10px',\n            color: '#ffffff',\n            marginTop: '4px',\n            padding: '6px',\n            backgroundColor: 'rgba(138, 43, 226, 0.2)',\n            borderRadius: '6px',\n            border: '1px solid rgba(138, 43, 226, 0.4)',\n            fontWeight: '500'\n          }}>\n            <strong>Variables:</strong> {variables.join(', ')}\n          </div>\n        )}\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,QAAQ,EAAEC,cAAc,EAAEC,YAAY,QAAQ,YAAY;AACnE,SAASC,QAAQ,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,IAAI,KAAI,WAAW,CAAC;EACnE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IAAEkB,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,CAAC;EACrE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAMsB,WAAW,GAAGpB,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMqB,gBAAgB,GAAIR,IAAI,IAAK;IACjC,MAAMS,KAAK,GAAG,qCAAqC;IACnD,MAAMC,OAAO,GAAG,EAAE;IAClB,IAAIC,KAAK;IAET,OAAO,CAACA,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACZ,IAAI,CAAC,MAAM,IAAI,EAAE;MAC1C,MAAMa,YAAY,GAAGF,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACD,OAAO,CAACI,QAAQ,CAACD,YAAY,CAAC,EAAE;QACnCH,OAAO,CAACK,IAAI,CAACF,YAAY,CAAC;MAC5B;IACF;IAEA,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,MAAMM,aAAa,GAAIhB,IAAI,IAAK;IAC9B,MAAMiB,KAAK,GAAGjB,IAAI,CAACkB,KAAK,CAAC,IAAI,CAAC;IAC9B,MAAMC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGJ,KAAK,CAACK,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC;;IAEjE;IACA,MAAMC,SAAS,GAAG,GAAG;IACrB,MAAMC,UAAU,GAAG,GAAG;;IAEtB;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC,CAAC;IACrB,MAAMC,YAAY,GAAGR,IAAI,CAACC,GAAG,CAACI,SAAS,EAAEL,IAAI,CAACS,GAAG,CAAC,GAAG,EAAEV,aAAa,GAAGQ,SAAS,GAAG,EAAE,CAAC,CAAC;;IAEvF;IACA,MAAMG,UAAU,GAAG,EAAE;IACrB,MAAMC,aAAa,GAAGX,IAAI,CAACC,GAAG,CAACK,UAAU,EAAET,KAAK,CAACO,MAAM,GAAGM,UAAU,GAAG,EAAE,CAAC;IAE1E,OAAO;MAAE3B,KAAK,EAAEyB,YAAY;MAAExB,MAAM,EAAE2B;IAAc,CAAC;EACvD,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,OAAO,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IAC9BrC,WAAW,CAACmC,OAAO,CAAC;;IAEpB;IACA,MAAMG,OAAO,GAAGrB,aAAa,CAACkB,OAAO,CAAC;IACtChC,WAAW,CAACmC,OAAO,CAAC;;IAEpB;IACA,MAAMC,YAAY,GAAG9B,gBAAgB,CAAC0B,OAAO,CAAC;IAC9C5B,YAAY,CAACgC,YAAY,CAAC;EAC5B,CAAC;;EAED;EACApD,SAAS,CAAC,MAAM;IACd,MAAMqD,gBAAgB,GAAG/B,gBAAgB,CAACV,QAAQ,CAAC;IACnDQ,YAAY,CAACiC,gBAAgB,CAAC;IAE9B,MAAMC,WAAW,GAAGxB,aAAa,CAAClB,QAAQ,CAAC;IAC3CI,WAAW,CAACsC,WAAW,CAAC;EAC1B,CAAC,EAAE,CAAC1C,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM2C,OAAO,GAAG,CACdpD,cAAc,CAACqD,WAAW,CAAC,QAAQ,CAAC,EACpC,GAAGrC,SAAS,CAACiB,GAAG,CAAC,CAACqB,QAAQ,EAAEC,KAAK,KAC/BtD,YAAY,CACVqD,QAAQ,EACR,QAAQ,EACRpD,QAAQ,CAACsD,IAAI,EACb;IAAEC,GAAG,EAAG,GAAE,EAAE,GAAIF,KAAK,GAAG,EAAI;EAAG,CACjC,CACF,CAAC,CACF;EAED,oBACEnD,OAAA,CAACL,QAAQ;IACPO,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXmD,KAAK,EAAC,MAAM;IACZN,OAAO,EAAEA,OAAQ;IACjBO,QAAQ,EAAC,MAAM;IACf7C,KAAK,EAAEF,QAAQ,CAACE,KAAM;IACtBC,MAAM,EAAEH,QAAQ,CAACG,MAAO;IAAA6C,QAAA,eAExBxD,OAAA;MAAKyD,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE,KAAK;QAAEjD,MAAM,EAAE;MAAO,CAAE;MAAA6C,QAAA,gBACnFxD,OAAA;QAAOyD,KAAK,EAAE;UACZI,QAAQ,EAAE,MAAM;UAChBH,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBG,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE;QACd,CAAE;QAAAP,QAAA,GAAC,OAED,eAAAxD,OAAA;UACEgE,GAAG,EAAElD,WAAY;UACjB6B,KAAK,EAAEtC,QAAS;UAChB4D,QAAQ,EAAE1B,gBAAiB;UAC3B2B,WAAW,EAAC,iDAAiD;UAC7DT,KAAK,EAAE;YACLU,SAAS,EAAE,KAAK;YAChBN,QAAQ,EAAE,MAAM;YAChBnD,KAAK,EAAE,MAAM;YACbC,MAAM,EAAG,GAAEH,QAAQ,CAACG,MAAM,GAAG,EAAG,IAAG;YACnCyD,MAAM,EAAE,MAAM;YACdC,MAAM,EAAE,mCAAmC;YAC3CC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,KAAK;YACdC,UAAU,EAAE,2CAA2C;YACvDC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,wCAAwC;YACpDC,eAAe,EAAE,uBAAuB;YACxCb,KAAK,EAAE,SAAS;YAChBc,SAAS,EAAE;UACb,CAAE;UACFC,OAAO,EAAGrC,CAAC,IAAK;YACdA,CAAC,CAACE,MAAM,CAACe,KAAK,CAACqB,WAAW,GAAG,yBAAyB;YACtDtC,CAAC,CAACE,MAAM,CAACe,KAAK,CAACmB,SAAS,GAAG,mCAAmC;YAC9DpC,CAAC,CAACE,MAAM,CAACe,KAAK,CAACkB,eAAe,GAAG,uBAAuB;UAC1D,CAAE;UACFI,MAAM,EAAGvC,CAAC,IAAK;YACbA,CAAC,CAACE,MAAM,CAACe,KAAK,CAACqB,WAAW,GAAG,yBAAyB;YACtDtC,CAAC,CAACE,MAAM,CAACe,KAAK,CAACmB,SAAS,GAAG,8BAA8B;YACzDpC,CAAC,CAACE,MAAM,CAACe,KAAK,CAACkB,eAAe,GAAG,uBAAuB;UAC1D;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,EAGPvE,SAAS,CAACmB,MAAM,GAAG,CAAC,iBACnB/B,OAAA;QAAKyD,KAAK,EAAE;UACVI,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,SAAS;UAChBK,SAAS,EAAE,KAAK;UAChBI,OAAO,EAAE,KAAK;UACdI,eAAe,EAAE,yBAAyB;UAC1CL,YAAY,EAAE,KAAK;UACnBD,MAAM,EAAE,mCAAmC;UAC3CN,UAAU,EAAE;QACd,CAAE;QAAAP,QAAA,gBACAxD,OAAA;UAAAwD,QAAA,EAAQ;QAAU;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACvE,SAAS,CAACwE,IAAI,CAAC,IAAI,CAAC;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAA/E,EAAA,CApJYH,QAAQ;AAAAoF,EAAA,GAARpF,QAAQ;AAAA,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}