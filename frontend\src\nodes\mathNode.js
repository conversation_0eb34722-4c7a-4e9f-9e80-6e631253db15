// mathNode.js
// Demonstrates mathematical operations with multiple inputs

import { useState } from 'react';
import { BaseNode, createHandle } from './BaseNode';
import { Position } from 'reactflow';

export const MathNode = ({ id, data }) => {
  const [operation, setOperation] = useState(data?.operation || 'add');

  const handleOperationChange = (e) => {
    setOperation(e.target.value);
  };

  const handles = [
    createHandle('input1', 'target', Position.Left, { top: '25%' }),
    createHandle('input2', 'target', Position.Left, { top: '75%' }),
    createHandle('result', 'source', Position.Right)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Math"
      handles={handles}
      nodeType="math"
    >
      <div>
        <label>
          Operation:
          <select
            value={operation}
            onChange={handleOperationChange}
            className="node-input"
          >
            <option value="add">Add</option>
            <option value="subtract">Subtract</option>
            <option value="multiply">Multiply</option>
            <option value="divide">Divide</option>
          </select>
        </label>
        <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>
          Performs {operation} operation
        </div>
      </div>
    </BaseNode>
  );
};
