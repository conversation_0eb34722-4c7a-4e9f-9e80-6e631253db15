{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\mathNode.js\",\n  _s = $RefreshSig$();\n// mathNode.js\n// Demonstrates mathematical operations with multiple inputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const MathNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [operation, setOperation] = useState((data === null || data === void 0 ? void 0 : data.operation) || 'add');\n  const handleOperationChange = e => {\n    setOperation(e.target.value);\n  };\n  const handles = [createHandle('input1', 'target', Position.Left, {\n    top: '25%'\n  }), createHandle('input2', 'target', Position.Left, {\n    top: '75%'\n  }), createHandle('result', 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Math\",\n    handles: handles,\n    nodeType: \"math\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px'\n        },\n        children: [\"Operation:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: operation,\n          onChange: handleOperationChange,\n          className: \"node-input\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"add\",\n            children: \"Add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"subtract\",\n            children: \"Subtract\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"multiply\",\n            children: \"Multiply\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"divide\",\n            children: \"Divide\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)'\n        },\n        children: [\"Performs \", operation, \" operation\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(MathNode, \"R7FWnHhCWCtLxw0vmLO8tIvK/QI=\");\n_c = MathNode;\nvar _c;\n$RefreshReg$(_c, \"MathNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "Position", "jsxDEV", "_jsxDEV", "MathNode", "id", "data", "_s", "operation", "setOperation", "handleOperationChange", "e", "target", "value", "handles", "Left", "top", "Right", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "fontSize", "onChange", "className", "cursor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/mathNode.js"], "sourcesContent": ["// mathNode.js\n// Demonstrates mathematical operations with multiple inputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\n\nexport const MathNode = ({ id, data }) => {\n  const [operation, setOperation] = useState(data?.operation || 'add');\n\n  const handleOperationChange = (e) => {\n    setOperation(e.target.value);\n  };\n\n  const handles = [\n    createHandle('input1', 'target', Position.Left, { top: '25%' }),\n    createHandle('input2', 'target', Position.Left, { top: '75%' }),\n    createHandle('result', 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Math\"\n      handles={handles}\n      nodeType=\"math\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={{ fontSize: '12px', display: 'flex', flexDirection: 'column', gap: '4px' }}>\n          Operation:\n          <select\n            value={operation}\n            onChange={handleOperationChange}\n            className=\"node-input\"\n            style={{ cursor: 'pointer' }}\n          >\n            <option value=\"add\">Add</option>\n            <option value=\"subtract\">Subtract</option>\n            <option value=\"multiply\">Multiply</option>\n            <option value=\"divide\">Divide</option>\n          </select>\n        </label>\n        <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>\n          Performs {operation} operation\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,YAAY;AACnD,SAASC,QAAQ,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,KAAK,CAAC;EAEpE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,OAAO,GAAG,CACdd,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAEC,QAAQ,CAACc,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EAC/DhB,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAEC,QAAQ,CAACc,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EAC/DhB,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAEC,QAAQ,CAACgB,KAAK,CAAC,CACjD;EAED,oBACEd,OAAA,CAACJ,QAAQ;IACPM,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXY,KAAK,EAAC,MAAM;IACZJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,MAAM;IAAAC,QAAA,eAEfjB,OAAA;MAAKkB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEjB,OAAA;QAAOkB,KAAK,EAAE;UAAEI,QAAQ,EAAE,MAAM;UAAEH,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAJ,QAAA,GAAC,YAExF,eAAAjB,OAAA;UACEU,KAAK,EAAEL,SAAU;UACjBkB,QAAQ,EAAEhB,qBAAsB;UAChCiB,SAAS,EAAC,YAAY;UACtBN,KAAK,EAAE;YAAEO,MAAM,EAAE;UAAU,CAAE;UAAAR,QAAA,gBAE7BjB,OAAA;YAAQU,KAAK,EAAC,KAAK;YAAAO,QAAA,EAAC;UAAG;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC7B,OAAA;YAAQU,KAAK,EAAC,UAAU;YAAAO,QAAA,EAAC;UAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C7B,OAAA;YAAQU,KAAK,EAAC,UAAU;YAAAO,QAAA,EAAC;UAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C7B,OAAA;YAAQU,KAAK,EAAC,QAAQ;YAAAO,QAAA,EAAC;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACR7B,OAAA;QAAKkB,KAAK,EAAE;UAAEI,QAAQ,EAAE,MAAM;UAAEQ,KAAK,EAAE;QAA2B,CAAE;QAAAb,QAAA,GAAC,WAC1D,EAACZ,SAAS,EAAC,YACtB;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACzB,EAAA,CA1CWH,QAAQ;AAAA8B,EAAA,GAAR9B,QAAQ;AAAA,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}