{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\BaseNode.js\";\n// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 220,\n  height = 100,\n  nodeType = 'default'\n}) => {\n  const defaultStyle = {\n    width,\n    height,\n    background: 'linear-gradient(135deg, rgba(26, 11, 46, 0.9) 0%, rgba(22, 33, 62, 0.9) 100%)',\n    border: '1px solid rgba(138, 43, 226, 0.4)',\n    borderRadius: '16px',\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Inter', sans-serif\",\n    position: 'relative',\n    color: '#ffffff',\n    backdropFilter: 'blur(20px)',\n    boxShadow: '0 8px 32px rgba(138, 43, 226, 0.2)',\n    ...style\n  };\n  const titleStyle = {\n    fontWeight: '600',\n    fontSize: '12px',\n    color: '#ffffff',\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: '1px solid rgba(138, 43, 226, 0.3)',\n    paddingBottom: theme.spacing.xs,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent'\n  };\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    fontSize: theme.typography.fontSize.xs\n  };\n  const handleStyle = {\n    width: '14px',\n    height: '14px',\n    background: 'linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)',\n    border: '2px solid #ffffff',\n    borderRadius: '50%',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    boxShadow: '0 2px 8px rgba(138, 43, 226, 0.4)'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: defaultStyle,\n    className: `base-node ${className}`,\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-1px)';\n      e.currentTarget.style.borderColor = '#6366f1';\n      e.currentTarget.style.background = '#3a3a3a';\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0)';\n      e.currentTarget.style.borderColor = '#4a4a4a';\n      e.currentTarget.style.background = '#2a2a2a';\n    },\n    children: [handles.map((handle, index) => /*#__PURE__*/_jsxDEV(Handle, {\n      type: handle.type,\n      position: handle.position,\n      id: `${id}-${handle.id}`,\n      style: {\n        ...handleStyle,\n        ...handle.style\n      }\n    }, `${id}-${handle.id || index}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this)), title && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: titleStyle,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: contentStyle,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to create handle configurations\n_c = BaseNode;\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),\n  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),\n  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),\n  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom)\n};\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Position", "theme", "jsxDEV", "_jsxDEV", "BaseNode", "id", "data", "title", "children", "handles", "style", "className", "width", "height", "nodeType", "defaultStyle", "background", "border", "borderRadius", "padding", "spacing", "md", "display", "flexDirection", "transition", "fontFamily", "position", "color", "<PERSON><PERSON>ilter", "boxShadow", "titleStyle", "fontWeight", "fontSize", "marginBottom", "xs", "textAlign", "borderBottom", "paddingBottom", "textTransform", "letterSpacing", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "contentStyle", "flex", "justifyContent", "typography", "handleStyle", "onMouseEnter", "e", "currentTarget", "transform", "borderColor", "onMouseLeave", "map", "handle", "index", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "createHandle", "HANDLE_CONFIGS", "sourceRight", "Right", "targetLeft", "Left", "targetTop", "Top", "sourceBottom", "Bottom", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/BaseNode.js"], "sourcesContent": ["// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\n\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 220,\n  height = 100,\n  nodeType = 'default'\n}) => {\n\n\n  const defaultStyle = {\n    width,\n    height,\n    background: 'linear-gradient(135deg, rgba(26, 11, 46, 0.9) 0%, rgba(22, 33, 62, 0.9) 100%)',\n    border: '1px solid rgba(138, 43, 226, 0.4)',\n    borderRadius: '16px',\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Inter', sans-serif\",\n    position: 'relative',\n    color: '#ffffff',\n    backdropFilter: 'blur(20px)',\n    boxShadow: '0 8px 32px rgba(138, 43, 226, 0.2)',\n    ...style\n  };\n\n  const titleStyle = {\n    fontWeight: '600',\n    fontSize: '12px',\n    color: '#ffffff',\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: '1px solid rgba(138, 43, 226, 0.3)',\n    paddingBottom: theme.spacing.xs,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n  };\n\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    fontSize: theme.typography.fontSize.xs,\n  };\n\n  const handleStyle = {\n    width: '14px',\n    height: '14px',\n    background: 'linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)',\n    border: '2px solid #ffffff',\n    borderRadius: '50%',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    boxShadow: '0 2px 8px rgba(138, 43, 226, 0.4)',\n  };\n\n  return (\n    <div\n      style={defaultStyle}\n      className={`base-node ${className}`}\n      onMouseEnter={(e) => {\n        e.currentTarget.style.transform = 'translateY(-1px)';\n        e.currentTarget.style.borderColor = '#6366f1';\n        e.currentTarget.style.background = '#3a3a3a';\n      }}\n      onMouseLeave={(e) => {\n        e.currentTarget.style.transform = 'translateY(0)';\n        e.currentTarget.style.borderColor = '#4a4a4a';\n        e.currentTarget.style.background = '#2a2a2a';\n      }}\n    >\n      {/* Render handles */}\n      {handles.map((handle, index) => (\n        <Handle\n          key={`${id}-${handle.id || index}`}\n          type={handle.type}\n          position={handle.position}\n          id={`${id}-${handle.id}`}\n          style={{\n            ...handleStyle,\n            ...handle.style\n          }}\n        />\n      ))}\n\n      {/* Title */}\n      {title && (\n        <div style={titleStyle}>\n          <span>{title}</span>\n        </div>\n      )}\n\n      {/* Content */}\n      <div style={contentStyle}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Helper function to create handle configurations\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),\n  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),\n  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),\n  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom),\n};\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,WAAW;AAC5C,SAASC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EACvBC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACRC,OAAO,GAAG,EAAE;EACZC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZC,QAAQ,GAAG;AACb,CAAC,KAAK;EAGJ,MAAMC,YAAY,GAAG;IACnBH,KAAK;IACLC,MAAM;IACNG,UAAU,EAAE,+EAA+E;IAC3FC,MAAM,EAAE,mCAAmC;IAC3CC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAElB,KAAK,CAACmB,OAAO,CAACC,EAAE;IACzBC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,wCAAwC;IACpDC,UAAU,EAAE,qBAAqB;IACjCC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,SAAS;IAChBC,cAAc,EAAE,YAAY;IAC5BC,SAAS,EAAE,oCAAoC;IAC/C,GAAGnB;EACL,CAAC;EAED,MAAMoB,UAAU,GAAG;IACjBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,MAAM;IAChBL,KAAK,EAAE,SAAS;IAChBM,YAAY,EAAEhC,KAAK,CAACmB,OAAO,CAACc,EAAE;IAC9BC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE,mCAAmC;IACjDC,aAAa,EAAEpC,KAAK,CAACmB,OAAO,CAACc,EAAE;IAC/BI,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,OAAO;IACtBvB,UAAU,EAAE,0CAA0C;IACtDwB,cAAc,EAAE,MAAM;IACtBC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE;EACvB,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE,CAAC;IACPtB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBsB,cAAc,EAAE,QAAQ;IACxBb,QAAQ,EAAE/B,KAAK,CAAC6C,UAAU,CAACd,QAAQ,CAACE;EACtC,CAAC;EAED,MAAMa,WAAW,GAAG;IAClBnC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdG,UAAU,EAAE,mDAAmD;IAC/DC,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,KAAK;IACnBM,UAAU,EAAE,wCAAwC;IACpDK,SAAS,EAAE;EACb,CAAC;EAED,oBACE1B,OAAA;IACEO,KAAK,EAAEK,YAAa;IACpBJ,SAAS,EAAG,aAAYA,SAAU,EAAE;IACpCqC,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAACxC,KAAK,CAACyC,SAAS,GAAG,kBAAkB;MACpDF,CAAC,CAACC,aAAa,CAACxC,KAAK,CAAC0C,WAAW,GAAG,SAAS;MAC7CH,CAAC,CAACC,aAAa,CAACxC,KAAK,CAACM,UAAU,GAAG,SAAS;IAC9C,CAAE;IACFqC,YAAY,EAAGJ,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAACxC,KAAK,CAACyC,SAAS,GAAG,eAAe;MACjDF,CAAC,CAACC,aAAa,CAACxC,KAAK,CAAC0C,WAAW,GAAG,SAAS;MAC7CH,CAAC,CAACC,aAAa,CAACxC,KAAK,CAACM,UAAU,GAAG,SAAS;IAC9C,CAAE;IAAAR,QAAA,GAGDC,OAAO,CAAC6C,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBrD,OAAA,CAACJ,MAAM;MAEL0D,IAAI,EAAEF,MAAM,CAACE,IAAK;MAClB/B,QAAQ,EAAE6B,MAAM,CAAC7B,QAAS;MAC1BrB,EAAE,EAAG,GAAEA,EAAG,IAAGkD,MAAM,CAAClD,EAAG,EAAE;MACzBK,KAAK,EAAE;QACL,GAAGqC,WAAW;QACd,GAAGQ,MAAM,CAAC7C;MACZ;IAAE,GAPI,GAAEL,EAAG,IAAGkD,MAAM,CAAClD,EAAE,IAAImD,KAAM,EAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQnC,CACF,CAAC,EAGDtD,KAAK,iBACJJ,OAAA;MAAKO,KAAK,EAAEoB,UAAW;MAAAtB,QAAA,eACrBL,OAAA;QAAAK,QAAA,EAAOD;MAAK;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD1D,OAAA;MAAKO,KAAK,EAAEiC,YAAa;MAAAnC,QAAA,EACtBA;IAAQ;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAC,EAAA,GA9Ga1D,QAAQ;AA+GrB,OAAO,MAAM2D,YAAY,GAAGA,CAAC1D,EAAE,EAAEoD,IAAI,EAAE/B,QAAQ,EAAEhB,KAAK,GAAG,CAAC,CAAC,MAAM;EAC/DL,EAAE;EACFoD,IAAI;EACJ/B,QAAQ;EACRhB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMsD,cAAc,GAAG;EAC5BC,WAAW,EAAEA,CAAC5D,EAAE,GAAG,QAAQ,KAAK0D,YAAY,CAAC1D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACkE,KAAK,CAAC;EAC1EC,UAAU,EAAEA,CAAC9D,EAAE,GAAG,OAAO,KAAK0D,YAAY,CAAC1D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACoE,IAAI,CAAC;EACvEC,SAAS,EAAEA,CAAChE,EAAE,GAAG,OAAO,KAAK0D,YAAY,CAAC1D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACsE,GAAG,CAAC;EACrEC,YAAY,EAAEA,CAAClE,EAAE,GAAG,QAAQ,KAAK0D,YAAY,CAAC1D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACwE,MAAM;AAC7E,CAAC;AAAC,IAAAV,EAAA;AAAAW,YAAA,CAAAX,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}