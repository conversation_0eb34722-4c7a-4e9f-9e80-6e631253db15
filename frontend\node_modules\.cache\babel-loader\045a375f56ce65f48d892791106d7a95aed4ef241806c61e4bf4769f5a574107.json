{"ast": null, "code": "// store.js\n\nimport { create } from \"zustand\";\nimport { addEdge, applyNode<PERSON>hang<PERSON>, applyEdgeChanges, MarkerType } from 'reactflow';\nexport const useStore = create((set, get) => ({\n  nodes: [],\n  edges: [],\n  nodeIDs: {},\n  getNodeID: type => {\n    const newIDs = {\n      ...get().nodeIDs\n    };\n    if (newIDs[type] === undefined) {\n      newIDs[type] = 0;\n    }\n    newIDs[type] += 1;\n    set({\n      nodeIDs: newIDs\n    });\n    return `${type}-${newIDs[type]}`;\n  },\n  addNode: node => {\n    set({\n      nodes: [...get().nodes, node]\n    });\n  },\n  onNodesChange: changes => {\n    set({\n      nodes: applyNodeChanges(changes, get().nodes)\n    });\n  },\n  onEdgesChange: changes => {\n    set({\n      edges: applyEdgeChanges(changes, get().edges)\n    });\n  },\n  onConnect: connection => {\n    set({\n      edges: addEdge({\n        ...connection,\n        type: 'smoothstep',\n        animated: true,\n        markerEnd: {\n          type: MarkerType.Arrow,\n          height: '20px',\n          width: '20px'\n        }\n      }, get().edges)\n    });\n  },\n  updateNodeField: (nodeId, fieldName, fieldValue) => {\n    set({\n      nodes: get().nodes.map(node => {\n        if (node.id === nodeId) {\n          node.data = {\n            ...node.data,\n            [fieldName]: fieldValue\n          };\n        }\n        return node;\n      })\n    });\n  }\n}));", "map": {"version": 3, "names": ["create", "addEdge", "applyNodeChanges", "applyEdgeChanges", "MarkerType", "useStore", "set", "get", "nodes", "edges", "nodeIDs", "getNodeID", "type", "newIDs", "undefined", "addNode", "node", "onNodesChange", "changes", "onEdgesChange", "onConnect", "connection", "animated", "markerEnd", "Arrow", "height", "width", "updateNodeField", "nodeId", "fieldName", "fieldValue", "map", "id", "data"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/store.js"], "sourcesContent": ["// store.js\n\nimport { create } from \"zustand\";\nimport {\n    addEdge,\n    applyNodeChang<PERSON>,\n    applyEdgeChanges,\n    MarkerType,\n  } from 'reactflow';\n\nexport const useStore = create((set, get) => ({\n    nodes: [],\n    edges: [],\n    nodeIDs: {},\n    getNodeID: (type) => {\n        const newIDs = {...get().nodeIDs};\n        if (newIDs[type] === undefined) {\n            newIDs[type] = 0;\n        }\n        newIDs[type] += 1;\n        set({nodeIDs: newIDs});\n        return `${type}-${newIDs[type]}`;\n    },\n    addNode: (node) => {\n        set({\n            nodes: [...get().nodes, node]\n        });\n    },\n    onNodesChange: (changes) => {\n      set({\n        nodes: applyNodeChanges(changes, get().nodes),\n      });\n    },\n    onEdgesChange: (changes) => {\n      set({\n        edges: applyEdgeChanges(changes, get().edges),\n      });\n    },\n    onConnect: (connection) => {\n      set({\n        edges: addEdge({...connection, type: 'smoothstep', animated: true, markerEnd: {type: MarkerType.Arrow, height: '20px', width: '20px'}}, get().edges),\n      });\n    },\n    updateNodeField: (nodeId, fieldName, fieldValue) => {\n      set({\n        nodes: get().nodes.map((node) => {\n          if (node.id === nodeId) {\n            node.data = { ...node.data, [fieldName]: fieldValue };\n          }\n  \n          return node;\n        }),\n      });\n    },\n  }));\n"], "mappings": "AAAA;;AAEA,SAASA,MAAM,QAAQ,SAAS;AAChC,SACIC,OAAO,EACPC,gBAAgB,EAChBC,gBAAgB,EAChBC,UAAU,QACL,WAAW;AAEpB,OAAO,MAAMC,QAAQ,GAAGL,MAAM,CAAC,CAACM,GAAG,EAAEC,GAAG,MAAM;EAC1CC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,EAAE;EACTC,OAAO,EAAE,CAAC,CAAC;EACXC,SAAS,EAAGC,IAAI,IAAK;IACjB,MAAMC,MAAM,GAAG;MAAC,GAAGN,GAAG,CAAC,CAAC,CAACG;IAAO,CAAC;IACjC,IAAIG,MAAM,CAACD,IAAI,CAAC,KAAKE,SAAS,EAAE;MAC5BD,MAAM,CAACD,IAAI,CAAC,GAAG,CAAC;IACpB;IACAC,MAAM,CAACD,IAAI,CAAC,IAAI,CAAC;IACjBN,GAAG,CAAC;MAACI,OAAO,EAAEG;IAAM,CAAC,CAAC;IACtB,OAAQ,GAAED,IAAK,IAAGC,MAAM,CAACD,IAAI,CAAE,EAAC;EACpC,CAAC;EACDG,OAAO,EAAGC,IAAI,IAAK;IACfV,GAAG,CAAC;MACAE,KAAK,EAAE,CAAC,GAAGD,GAAG,CAAC,CAAC,CAACC,KAAK,EAAEQ,IAAI;IAChC,CAAC,CAAC;EACN,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IAC1BZ,GAAG,CAAC;MACFE,KAAK,EAAEN,gBAAgB,CAACgB,OAAO,EAAEX,GAAG,CAAC,CAAC,CAACC,KAAK;IAC9C,CAAC,CAAC;EACJ,CAAC;EACDW,aAAa,EAAGD,OAAO,IAAK;IAC1BZ,GAAG,CAAC;MACFG,KAAK,EAAEN,gBAAgB,CAACe,OAAO,EAAEX,GAAG,CAAC,CAAC,CAACE,KAAK;IAC9C,CAAC,CAAC;EACJ,CAAC;EACDW,SAAS,EAAGC,UAAU,IAAK;IACzBf,GAAG,CAAC;MACFG,KAAK,EAAER,OAAO,CAAC;QAAC,GAAGoB,UAAU;QAAET,IAAI,EAAE,YAAY;QAAEU,QAAQ,EAAE,IAAI;QAAEC,SAAS,EAAE;UAACX,IAAI,EAAER,UAAU,CAACoB,KAAK;UAAEC,MAAM,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAM;MAAC,CAAC,EAAEnB,GAAG,CAAC,CAAC,CAACE,KAAK;IACrJ,CAAC,CAAC;EACJ,CAAC;EACDkB,eAAe,EAAEA,CAACC,MAAM,EAAEC,SAAS,EAAEC,UAAU,KAAK;IAClDxB,GAAG,CAAC;MACFE,KAAK,EAAED,GAAG,CAAC,CAAC,CAACC,KAAK,CAACuB,GAAG,CAAEf,IAAI,IAAK;QAC/B,IAAIA,IAAI,CAACgB,EAAE,KAAKJ,MAAM,EAAE;UACtBZ,IAAI,CAACiB,IAAI,GAAG;YAAE,GAAGjB,IAAI,CAACiB,IAAI;YAAE,CAACJ,SAAS,GAAGC;UAAW,CAAC;QACvD;QAEA,OAAOd,IAAI;MACb,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}