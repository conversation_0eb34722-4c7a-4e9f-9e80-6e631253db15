{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\toolbar.js\";\n// toolbar.js\n\nimport { DraggableNode } from './draggableNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PipelineToolbar = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pipeline-toolbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-brand\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"brand-logo\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"40\",\n            height: \"40\",\n            viewBox: \"0 0 40 40\",\n            className: \"logo-svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n              children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                id: \"logoGradient\",\n                x1: \"0%\",\n                y1: \"0%\",\n                x2: \"100%\",\n                y2: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"0%\",\n                  stopColor: \"#8b5cf6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 15,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"100%\",\n                  stopColor: \"#6366f1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 16,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 14,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8 32L20 8L32 32H8Z\",\n              fill: \"url(#logoGradient)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"20\",\n              cy: \"20\",\n              r: \"3\",\n              fill: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"brand-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"toolbar-title\",\n            children: \"VectorShift Pipeline Builder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"toolbar-subtitle\",\n            children: \"Leverage AI across data of all formats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-title\",\n        children: \"\\uD83D\\uDCC4 Data Sources\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-nodes\",\n        children: [/*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"customInput\",\n          label: \"Document\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"text\",\n          label: \"Text\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-title\",\n        children: \"\\uD83E\\uDD16 AI Processing\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-nodes\",\n        children: [/*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"llm\",\n          label: \"AI Model\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"filter\",\n          label: \"Filter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"aggregator\",\n          label: \"Aggregator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-title\",\n        children: \"\\u2699\\uFE0F Logic & Output\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-nodes\",\n        children: [/*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"math\",\n          label: \"Math\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"switch\",\n          label: \"Switch\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"timer\",\n          label: \"Timer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DraggableNode, {\n          type: \"customOutput\",\n          label: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n_c = PipelineToolbar;\nvar _c;\n$RefreshReg$(_c, \"PipelineToolbar\");", "map": {"version": 3, "names": ["DraggableNode", "jsxDEV", "_jsxDEV", "PipelineToolbar", "className", "children", "width", "height", "viewBox", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "fill", "cx", "cy", "r", "type", "label", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/toolbar.js"], "sourcesContent": ["// toolbar.js\n\nimport { DraggableNode } from './draggableNode';\n\nexport const PipelineToolbar = () => {\n\n    return (\n        <div className=\"pipeline-toolbar\">\n            <div className=\"toolbar-header\">\n                <div className=\"toolbar-brand\">\n                    <div className=\"brand-logo\">\n                        <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" className=\"logo-svg\">\n                            <defs>\n                                <linearGradient id=\"logoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                                    <stop offset=\"0%\" stopColor=\"#8b5cf6\" />\n                                    <stop offset=\"100%\" stopColor=\"#6366f1\" />\n                                </linearGradient>\n                            </defs>\n                            <path d=\"M8 32L20 8L32 32H8Z\" fill=\"url(#logoGradient)\" />\n                            <circle cx=\"20\" cy=\"20\" r=\"3\" fill=\"white\" />\n                        </svg>\n                    </div>\n                    <div className=\"brand-text\">\n                        <div className=\"toolbar-title\">\n                            VectorShift Pipeline Builder\n                        </div>\n                        <div className=\"toolbar-subtitle\">\n                            Leverage AI across data of all formats\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"toolbar-section\">\n                <div className=\"section-title\">📄 Data Sources</div>\n                <div className=\"toolbar-nodes\">\n                    <DraggableNode type='customInput' label='Document' />\n                    <DraggableNode type='text' label='Text' />\n                </div>\n            </div>\n\n            <div className=\"toolbar-section\">\n                <div className=\"section-title\">🤖 AI Processing</div>\n                <div className=\"toolbar-nodes\">\n                    <DraggableNode type='llm' label='AI Model' />\n                    <DraggableNode type='filter' label='Filter' />\n                    <DraggableNode type='aggregator' label='Aggregator' />\n                </div>\n            </div>\n\n            <div className=\"toolbar-section\">\n                <div className=\"section-title\">⚙️ Logic & Output</div>\n                <div className=\"toolbar-nodes\">\n                    <DraggableNode type='math' label='Math' />\n                    <DraggableNode type='switch' label='Switch' />\n                    <DraggableNode type='timer' label='Timer' />\n                    <DraggableNode type='customOutput' label='Output' />\n                </div>\n            </div>\n        </div>\n    );\n};\n"], "mappings": ";AAAA;;AAEA,SAASA,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAEjC,oBACID,OAAA;IAAKE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC7BH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BH,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvBH,OAAA;YAAKI,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACJ,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAChEH,OAAA;cAAAG,QAAA,eACIH,OAAA;gBAAgBO,EAAE,EAAC,cAAc;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,MAAM;gBAAAR,QAAA,gBACjEH,OAAA;kBAAMY,MAAM,EAAC,IAAI;kBAACC,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxCjB,OAAA;kBAAMY,MAAM,EAAC,MAAM;kBAACC,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACPjB,OAAA;cAAMkB,CAAC,EAAC,qBAAqB;cAACC,IAAI,EAAC;YAAoB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DjB,OAAA;cAAQoB,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,CAAC,EAAC,GAAG;cAACH,IAAI,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNjB,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBH,OAAA;YAAKE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNjB,OAAA;YAAKE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAElC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENjB,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAe;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpDjB,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BH,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,aAAa;UAACC,KAAK,EAAC;QAAU;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDjB,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC;QAAM;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENjB,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrDjB,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BH,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,KAAK;UAACC,KAAK,EAAC;QAAU;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CjB,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC;QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CjB,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,YAAY;UAACC,KAAK,EAAC;QAAY;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENjB,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtDjB,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BH,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC;QAAM;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CjB,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC;QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CjB,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,OAAO;UAACC,KAAK,EAAC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CjB,OAAA,CAACF,aAAa;UAACyB,IAAI,EAAC,cAAc;UAACC,KAAK,EAAC;QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACQ,EAAA,GAzDWxB,eAAe;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}