// NodeInput.js
// Reusable input component for node properties with overflow handling and tooltips

import React from 'react';

export const NodeInput = ({
  value,
  onChange,
  placeholder = "",
  label = "",
  showTooltip = true,
  style = {},
  ...props
}) => {
  const shouldShowTooltip = showTooltip && value && value.length > 15;

  return (
    <div className="input-container">
      {shouldShowTooltip && (
        <div className="input-tooltip">
          {value}
        </div>
      )}
      <input
        type="text"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className="node-input"

        style={style}
        {...props}
      />
    </div>
  );
};
