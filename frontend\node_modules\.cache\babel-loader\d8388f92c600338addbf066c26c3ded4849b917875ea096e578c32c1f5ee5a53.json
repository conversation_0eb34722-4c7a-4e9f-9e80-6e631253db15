{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\BaseNode.js\";\n// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 220,\n  height = 100,\n  nodeType = 'default'\n}) => {\n  const getNodeTypeColor = type => {\n    return theme.colors.nodeTypes[type] || theme.colors.gray[50];\n  };\n  const defaultStyle = {\n    width,\n    height,\n    background: `linear-gradient(135deg, rgba(140, 79, 255, 0.15) 0%, rgba(183, 112, 255, 0.1) 100%)`,\n    backdropFilter: 'blur(10px)',\n    border: `2px solid rgba(140, 79, 255, 0.3)`,\n    borderRadius: '16px',\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    boxShadow: `\n      0 8px 16px rgba(140, 79, 255, 0.2),\n      0 0 0 1px rgba(140, 79, 255, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.1)\n    `,\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n    position: 'relative',\n    overflow: 'hidden',\n    color: '#FFFFFF',\n    ...style\n  };\n  const titleStyle = {\n    fontWeight: '700',\n    fontSize: '14px',\n    color: '#FFFFFF',\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: `1px solid rgba(140, 79, 255, 0.3)`,\n    paddingBottom: theme.spacing.xs,\n    textTransform: 'uppercase',\n    letterSpacing: '1px'\n  };\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    fontSize: theme.typography.fontSize.xs\n  };\n  const handleStyle = {\n    width: '14px',\n    height: '14px',\n    background: 'linear-gradient(135deg, #8C4FFF 0%, #B770FF 100%)',\n    border: `2px solid rgba(255, 255, 255, 0.8)`,\n    borderRadius: '50%',\n    transition: 'all 200ms ease',\n    boxShadow: '0 0 8px rgba(140, 79, 255, 0.6)'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: defaultStyle,\n    className: `base-node ${className}`,\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';\n      e.currentTarget.style.boxShadow = `\n          0 16px 32px rgba(140, 79, 255, 0.3),\n          0 0 0 1px rgba(140, 79, 255, 0.4),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `;\n      e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.5)';\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0) scale(1)';\n      e.currentTarget.style.boxShadow = `\n          0 8px 16px rgba(140, 79, 255, 0.2),\n          0 0 0 1px rgba(140, 79, 255, 0.1),\n          inset 0 1px 0 rgba(255, 255, 255, 0.1)\n        `;\n      e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.3)';\n    },\n    children: [handles.map((handle, index) => /*#__PURE__*/_jsxDEV(Handle, {\n      type: handle.type,\n      position: handle.position,\n      id: `${id}-${handle.id}`,\n      style: {\n        ...handleStyle,\n        ...handle.style\n      }\n    }, `${id}-${handle.id || index}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this)), title && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: titleStyle,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: contentStyle,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to create handle configurations\n_c = BaseNode;\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),\n  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),\n  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),\n  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom)\n};\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Position", "theme", "jsxDEV", "_jsxDEV", "BaseNode", "id", "data", "title", "children", "handles", "style", "className", "width", "height", "nodeType", "getNodeTypeColor", "type", "colors", "nodeTypes", "gray", "defaultStyle", "background", "<PERSON><PERSON>ilter", "border", "borderRadius", "padding", "spacing", "md", "display", "flexDirection", "boxShadow", "transition", "fontFamily", "position", "overflow", "color", "titleStyle", "fontWeight", "fontSize", "marginBottom", "xs", "textAlign", "borderBottom", "paddingBottom", "textTransform", "letterSpacing", "contentStyle", "flex", "justifyContent", "typography", "handleStyle", "onMouseEnter", "e", "currentTarget", "transform", "borderColor", "onMouseLeave", "map", "handle", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "createHandle", "HANDLE_CONFIGS", "sourceRight", "Right", "targetLeft", "Left", "targetTop", "Top", "sourceBottom", "Bottom", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/BaseNode.js"], "sourcesContent": ["// BaseNode.js\n// Reusable base component for all node types\n\nimport { Hand<PERSON>, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\n\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 220,\n  height = 100,\n  nodeType = 'default'\n}) => {\n  const getNodeTypeColor = (type) => {\n    return theme.colors.nodeTypes[type] || theme.colors.gray[50];\n  };\n\n  const defaultStyle = {\n    width,\n    height,\n    background: `linear-gradient(135deg, rgba(140, 79, 255, 0.15) 0%, rgba(183, 112, 255, 0.1) 100%)`,\n    backdropFilter: 'blur(10px)',\n    border: `2px solid rgba(140, 79, 255, 0.3)`,\n    borderRadius: '16px',\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    boxShadow: `\n      0 8px 16px rgba(140, 79, 255, 0.2),\n      0 0 0 1px rgba(140, 79, 255, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.1)\n    `,\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n    position: 'relative',\n    overflow: 'hidden',\n    color: '#FFFFFF',\n    ...style\n  };\n\n  const titleStyle = {\n    fontWeight: '700',\n    fontSize: '14px',\n    color: '#FFFFFF',\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: `1px solid rgba(140, 79, 255, 0.3)`,\n    paddingBottom: theme.spacing.xs,\n    textTransform: 'uppercase',\n    letterSpacing: '1px',\n  };\n\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    fontSize: theme.typography.fontSize.xs,\n  };\n\n  const handleStyle = {\n    width: '14px',\n    height: '14px',\n    background: 'linear-gradient(135deg, #8C4FFF 0%, #B770FF 100%)',\n    border: `2px solid rgba(255, 255, 255, 0.8)`,\n    borderRadius: '50%',\n    transition: 'all 200ms ease',\n    boxShadow: '0 0 8px rgba(140, 79, 255, 0.6)',\n  };\n\n  return (\n    <div\n      style={defaultStyle}\n      className={`base-node ${className}`}\n      onMouseEnter={(e) => {\n        e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';\n        e.currentTarget.style.boxShadow = `\n          0 16px 32px rgba(140, 79, 255, 0.3),\n          0 0 0 1px rgba(140, 79, 255, 0.4),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `;\n        e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.5)';\n      }}\n      onMouseLeave={(e) => {\n        e.currentTarget.style.transform = 'translateY(0) scale(1)';\n        e.currentTarget.style.boxShadow = `\n          0 8px 16px rgba(140, 79, 255, 0.2),\n          0 0 0 1px rgba(140, 79, 255, 0.1),\n          inset 0 1px 0 rgba(255, 255, 255, 0.1)\n        `;\n        e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.3)';\n      }}\n    >\n      {/* Render handles */}\n      {handles.map((handle, index) => (\n        <Handle\n          key={`${id}-${handle.id || index}`}\n          type={handle.type}\n          position={handle.position}\n          id={`${id}-${handle.id}`}\n          style={{\n            ...handleStyle,\n            ...handle.style\n          }}\n        />\n      ))}\n\n      {/* Title */}\n      {title && (\n        <div style={titleStyle}>\n          <span>{title}</span>\n        </div>\n      )}\n\n      {/* Content */}\n      <div style={contentStyle}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Helper function to create handle configurations\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),\n  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),\n  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),\n  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom),\n};\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,WAAW;AAC5C,SAASC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EACvBC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACRC,OAAO,GAAG,EAAE;EACZC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAIC,IAAI,IAAK;IACjC,OAAOf,KAAK,CAACgB,MAAM,CAACC,SAAS,CAACF,IAAI,CAAC,IAAIf,KAAK,CAACgB,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC;EAC9D,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBR,KAAK;IACLC,MAAM;IACNQ,UAAU,EAAG,qFAAoF;IACjGC,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAG,mCAAkC;IAC3CC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAExB,KAAK,CAACyB,OAAO,CAACC,EAAE;IACzBC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAG;AAChB;AACA;AACA;AACA,KAAK;IACDC,UAAU,EAAE,wCAAwC;IACpDC,UAAU,EAAE,2CAA2C;IACvDC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,SAAS;IAChB,GAAGzB;EACL,CAAC;EAED,MAAM0B,UAAU,GAAG;IACjBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,MAAM;IAChBH,KAAK,EAAE,SAAS;IAChBI,YAAY,EAAEtC,KAAK,CAACyB,OAAO,CAACc,EAAE;IAC9BC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAG,mCAAkC;IACjDC,aAAa,EAAE1C,KAAK,CAACyB,OAAO,CAACc,EAAE;IAC/BI,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE;EACjB,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE,CAAC;IACPnB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBmB,cAAc,EAAE,QAAQ;IACxBV,QAAQ,EAAErC,KAAK,CAACgD,UAAU,CAACX,QAAQ,CAACE;EACtC,CAAC;EAED,MAAMU,WAAW,GAAG;IAClBtC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdQ,UAAU,EAAE,mDAAmD;IAC/DE,MAAM,EAAG,oCAAmC;IAC5CC,YAAY,EAAE,KAAK;IACnBO,UAAU,EAAE,gBAAgB;IAC5BD,SAAS,EAAE;EACb,CAAC;EAED,oBACE3B,OAAA;IACEO,KAAK,EAAEU,YAAa;IACpBT,SAAS,EAAG,aAAYA,SAAU,EAAE;IACpCwC,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAAC3C,KAAK,CAAC4C,SAAS,GAAG,8BAA8B;MAChEF,CAAC,CAACC,aAAa,CAAC3C,KAAK,CAACoB,SAAS,GAAI;AAC3C;AACA;AACA;AACA,SAAS;MACDsB,CAAC,CAACC,aAAa,CAAC3C,KAAK,CAAC6C,WAAW,GAAG,yBAAyB;IAC/D,CAAE;IACFC,YAAY,EAAGJ,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAAC3C,KAAK,CAAC4C,SAAS,GAAG,wBAAwB;MAC1DF,CAAC,CAACC,aAAa,CAAC3C,KAAK,CAACoB,SAAS,GAAI;AAC3C;AACA;AACA;AACA,SAAS;MACDsB,CAAC,CAACC,aAAa,CAAC3C,KAAK,CAAC6C,WAAW,GAAG,yBAAyB;IAC/D,CAAE;IAAA/C,QAAA,GAGDC,OAAO,CAACgD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBxD,OAAA,CAACJ,MAAM;MAELiB,IAAI,EAAE0C,MAAM,CAAC1C,IAAK;MAClBiB,QAAQ,EAAEyB,MAAM,CAACzB,QAAS;MAC1B5B,EAAE,EAAG,GAAEA,EAAG,IAAGqD,MAAM,CAACrD,EAAG,EAAE;MACzBK,KAAK,EAAE;QACL,GAAGwC,WAAW;QACd,GAAGQ,MAAM,CAAChD;MACZ;IAAE,GAPI,GAAEL,EAAG,IAAGqD,MAAM,CAACrD,EAAE,IAAIsD,KAAM,EAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQnC,CACF,CAAC,EAGDxD,KAAK,iBACJJ,OAAA;MAAKO,KAAK,EAAE0B,UAAW;MAAA5B,QAAA,eACrBL,OAAA;QAAAK,QAAA,EAAOD;MAAK;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD5D,OAAA;MAAKO,KAAK,EAAEoC,YAAa;MAAAtC,QAAA,EACtBA;IAAQ;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAC,EAAA,GAzHa5D,QAAQ;AA0HrB,OAAO,MAAM6D,YAAY,GAAGA,CAAC5D,EAAE,EAAEW,IAAI,EAAEiB,QAAQ,EAAEvB,KAAK,GAAG,CAAC,CAAC,MAAM;EAC/DL,EAAE;EACFW,IAAI;EACJiB,QAAQ;EACRvB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMwD,cAAc,GAAG;EAC5BC,WAAW,EAAEA,CAAC9D,EAAE,GAAG,QAAQ,KAAK4D,YAAY,CAAC5D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACoE,KAAK,CAAC;EAC1EC,UAAU,EAAEA,CAAChE,EAAE,GAAG,OAAO,KAAK4D,YAAY,CAAC5D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACsE,IAAI,CAAC;EACvEC,SAAS,EAAEA,CAAClE,EAAE,GAAG,OAAO,KAAK4D,YAAY,CAAC5D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACwE,GAAG,CAAC;EACrEC,YAAY,EAAEA,CAACpE,EAAE,GAAG,QAAQ,KAAK4D,YAAY,CAAC5D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAAC0E,MAAM;AAC7E,CAAC;AAAC,IAAAV,EAAA;AAAAW,YAAA,CAAAX,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}