// outputNode.js

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS } from './BaseNode';

export const OutputNode = ({ id, data }) => {
  const [currName, setCurrName] = useState(data?.outputName || id.replace('customOutput-', 'output_'));
  const [outputType, setOutputType] = useState(data.outputType || 'Text');

  const handleNameChange = (e) => {
    setCurrName(e.target.value);
  };

  const handleTypeChange = (e) => {
    setOutputType(e.target.value);
  };

  const handles = [HANDLE_CONFIGS.targetLeft('value')];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Output"
      handles={handles}
      nodeType="output"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <label style={{ fontSize: '12px' }}>
          Name:
          <input
            type="text"
            value={currName}
            onChange={handleNameChange}
            style={{ marginLeft: '4px', fontSize: '11px', width: '100px' }}
          />
        </label>
        <label style={{ fontSize: '12px' }}>
          Type:
          <select
            value={outputType}
            onChange={handleTypeChange}
            style={{ marginLeft: '4px', fontSize: '11px', width: '100px' }}
          >
            <option value="Text">Text</option>
            <option value="File">Image</option>
          </select>
        </label>
      </div>
    </BaseNode>
  );
}
