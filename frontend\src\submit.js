// submit.js

import { useStore } from './store';
import { shallow } from 'zustand/shallow';

const selector = (state) => ({
  nodes: state.nodes,
  edges: state.edges,
});

export const SubmitButton = () => {
    const { nodes, edges } = useStore(selector, shallow);

    const handleSubmit = async () => {
        try {
            // Prepare pipeline data
            const pipelineData = {
                nodes: nodes,
                edges: edges
            };

            // Send to backend
            const response = await fetch('http://localhost:8000/pipelines/parse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(pipelineData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            // Create user-friendly alert message
            const dagStatus = result.is_dag ? "✅ Valid DAG" : "❌ Contains Cycles";
            const message = `Pipeline Analysis Results:

📊 Number of Nodes: ${result.num_nodes}
🔗 Number of Edges: ${result.num_edges}
🔄 DAG Status: ${dagStatus}

${result.is_dag ?
    "Great! Your pipeline is a valid Directed Acyclic Graph." :
    "Warning: Your pipeline contains cycles and is not a valid DAG."}`;

            alert(message);

        } catch (error) {
            console.error('Error submitting pipeline:', error);
            alert(`Error submitting pipeline: ${error.message}`);
        }
    };

    return (
        <div className="submit-container">
            <button
                type="button"
                className="submit-button"
                onClick={handleSubmit}
            >
                Submit Pipeline
            </button>
        </div>
    );
}
