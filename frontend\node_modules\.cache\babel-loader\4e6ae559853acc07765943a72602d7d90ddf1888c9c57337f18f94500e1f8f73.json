{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\timerNode.js\",\n  _s = $RefreshSig$();\n// timerNode.js\n// Demonstrates a timer/delay node with configurable duration\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TimerNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [duration, setDuration] = useState((data === null || data === void 0 ? void 0 : data.duration) || 1000);\n  const [unit, setUnit] = useState((data === null || data === void 0 ? void 0 : data.unit) || 'ms');\n  const handleDurationChange = e => {\n    setDuration(parseInt(e.target.value) || 0);\n  };\n  const handleUnitChange = e => {\n    setUnit(e.target.value);\n  };\n  const handles = [HANDLE_CONFIGS.targetLeft('trigger'), HANDLE_CONFIGS.sourceRight('delayed')];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Timer\",\n    handles: handles,\n    nodeType: \"timer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"inline-label\",\n        children: [\"Duration:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: duration,\n          onChange: handleDurationChange,\n          className: \"node-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"inline-label\",\n        children: [\"Unit:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: unit,\n          onChange: handleUnitChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"ms\",\n            children: \"ms\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"s\",\n            children: \"s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"m\",\n            children: \"m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(TimerNode, \"m+3RnTue3OQ7QWY94W+/xWdhgNE=\");\n_c = TimerNode;\nvar _c;\n$RefreshReg$(_c, \"TimerNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "jsxDEV", "_jsxDEV", "TimerNode", "id", "data", "_s", "duration", "setDuration", "unit", "setUnit", "handleDurationChange", "e", "parseInt", "target", "value", "handleUnitChange", "handles", "targetLeft", "sourceRight", "title", "nodeType", "children", "className", "type", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/timerNode.js"], "sourcesContent": ["// timerNode.js\n// Demonstrates a timer/delay node with configurable duration\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS } from './BaseNode';\n\nexport const TimerNode = ({ id, data }) => {\n  const [duration, setDuration] = useState(data?.duration || 1000);\n  const [unit, setUnit] = useState(data?.unit || 'ms');\n\n  const handleDurationChange = (e) => {\n    setDuration(parseInt(e.target.value) || 0);\n  };\n\n  const handleUnitChange = (e) => {\n    setUnit(e.target.value);\n  };\n\n  const handles = [\n    HANDLE_CONFIGS.targetLeft('trigger'),\n    HANDLE_CONFIGS.sourceRight('delayed')\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Timer\"\n      handles={handles}\n      nodeType=\"timer\"\n    >\n      <div>\n        <label className=\"inline-label\">\n          Duration:\n          <input\n            type=\"number\"\n            value={duration}\n            onChange={handleDurationChange}\n            className=\"node-input\"\n          />\n        </label>\n        <label className=\"inline-label\">\n          Unit:\n          <select\n            value={unit}\n            onChange={handleUnitChange}\n            className=\"node-input\"\n          >\n            <option value=\"ms\">ms</option>\n            <option value=\"s\">s</option>\n            <option value=\"m\">m</option>\n          </select>\n        </label>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,CAAAO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,QAAQ,KAAI,IAAI,CAAC;EAChE,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,CAAAO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,IAAI,KAAI,IAAI,CAAC;EAEpD,MAAME,oBAAoB,GAAIC,CAAC,IAAK;IAClCJ,WAAW,CAACK,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,gBAAgB,GAAIJ,CAAC,IAAK;IAC9BF,OAAO,CAACE,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;EACzB,CAAC;EAED,MAAME,OAAO,GAAG,CACdjB,cAAc,CAACkB,UAAU,CAAC,SAAS,CAAC,EACpClB,cAAc,CAACmB,WAAW,CAAC,SAAS,CAAC,CACtC;EAED,oBACEjB,OAAA,CAACH,QAAQ;IACPK,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAC,OAAO;IACbH,OAAO,EAAEA,OAAQ;IACjBI,QAAQ,EAAC,OAAO;IAAAC,QAAA,eAEhBpB,OAAA;MAAAoB,QAAA,gBACEpB,OAAA;QAAOqB,SAAS,EAAC,cAAc;QAAAD,QAAA,GAAC,WAE9B,eAAApB,OAAA;UACEsB,IAAI,EAAC,QAAQ;UACbT,KAAK,EAAER,QAAS;UAChBkB,QAAQ,EAAEd,oBAAqB;UAC/BY,SAAS,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACR3B,OAAA;QAAOqB,SAAS,EAAC,cAAc;QAAAD,QAAA,GAAC,OAE9B,eAAApB,OAAA;UACEa,KAAK,EAAEN,IAAK;UACZgB,QAAQ,EAAET,gBAAiB;UAC3BO,SAAS,EAAC,YAAY;UAAAD,QAAA,gBAEtBpB,OAAA;YAAQa,KAAK,EAAC,IAAI;YAAAO,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B3B,OAAA;YAAQa,KAAK,EAAC,GAAG;YAAAO,QAAA,EAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5B3B,OAAA;YAAQa,KAAK,EAAC,GAAG;YAAAO,QAAA,EAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACvB,EAAA,CAlDWH,SAAS;AAAA2B,EAAA,GAAT3B,SAAS;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}