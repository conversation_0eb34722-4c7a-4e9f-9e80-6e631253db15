{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\BaseNode.js\";\n// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 220,\n  height = 100,\n  nodeType = 'default'\n}) => {\n  const getNodeTypeColor = type => {\n    return theme.colors.nodeTypes[type] || theme.colors.gray[50];\n  };\n  const defaultStyle = {\n    width,\n    height,\n    background: '#2a2a2a',\n    border: '1px solid #4a4a4a',\n    borderRadius: '8px',\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    transition: 'all 200ms ease',\n    fontFamily: \"'Inter', sans-serif\",\n    position: 'relative',\n    color: '#ffffff',\n    ...style\n  };\n  const titleStyle = {\n    fontWeight: '600',\n    fontSize: '12px',\n    color: '#ffffff',\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: '1px solid #4a4a4a',\n    paddingBottom: theme.spacing.xs,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px'\n  };\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    fontSize: theme.typography.fontSize.xs\n  };\n  const handleStyle = {\n    width: '14px',\n    height: '14px',\n    background: 'linear-gradient(135deg, #8C4FFF 0%, #B770FF 100%)',\n    border: `2px solid rgba(255, 255, 255, 0.8)`,\n    borderRadius: '50%',\n    transition: 'all 200ms ease',\n    boxShadow: '0 0 8px rgba(140, 79, 255, 0.6)'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: defaultStyle,\n    className: `base-node ${className}`,\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';\n      e.currentTarget.style.boxShadow = `\n          0 16px 32px rgba(140, 79, 255, 0.3),\n          0 0 0 1px rgba(140, 79, 255, 0.4),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `;\n      e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.5)';\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0) scale(1)';\n      e.currentTarget.style.boxShadow = `\n          0 8px 16px rgba(140, 79, 255, 0.2),\n          0 0 0 1px rgba(140, 79, 255, 0.1),\n          inset 0 1px 0 rgba(255, 255, 255, 0.1)\n        `;\n      e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.3)';\n    },\n    children: [handles.map((handle, index) => /*#__PURE__*/_jsxDEV(Handle, {\n      type: handle.type,\n      position: handle.position,\n      id: `${id}-${handle.id}`,\n      style: {\n        ...handleStyle,\n        ...handle.style\n      }\n    }, `${id}-${handle.id || index}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 9\n    }, this)), title && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: titleStyle,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: contentStyle,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to create handle configurations\n_c = BaseNode;\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),\n  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),\n  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),\n  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom)\n};\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Position", "theme", "jsxDEV", "_jsxDEV", "BaseNode", "id", "data", "title", "children", "handles", "style", "className", "width", "height", "nodeType", "getNodeTypeColor", "type", "colors", "nodeTypes", "gray", "defaultStyle", "background", "border", "borderRadius", "padding", "spacing", "md", "display", "flexDirection", "transition", "fontFamily", "position", "color", "titleStyle", "fontWeight", "fontSize", "marginBottom", "xs", "textAlign", "borderBottom", "paddingBottom", "textTransform", "letterSpacing", "contentStyle", "flex", "justifyContent", "typography", "handleStyle", "boxShadow", "onMouseEnter", "e", "currentTarget", "transform", "borderColor", "onMouseLeave", "map", "handle", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "createHandle", "HANDLE_CONFIGS", "sourceRight", "Right", "targetLeft", "Left", "targetTop", "Top", "sourceBottom", "Bottom", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/BaseNode.js"], "sourcesContent": ["// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\n\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 220,\n  height = 100,\n  nodeType = 'default'\n}) => {\n  const getNodeTypeColor = (type) => {\n    return theme.colors.nodeTypes[type] || theme.colors.gray[50];\n  };\n\n  const defaultStyle = {\n    width,\n    height,\n    background: '#2a2a2a',\n    border: '1px solid #4a4a4a',\n    borderRadius: '8px',\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    transition: 'all 200ms ease',\n    fontFamily: \"'Inter', sans-serif\",\n    position: 'relative',\n    color: '#ffffff',\n    ...style\n  };\n\n  const titleStyle = {\n    fontWeight: '600',\n    fontSize: '12px',\n    color: '#ffffff',\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: '1px solid #4a4a4a',\n    paddingBottom: theme.spacing.xs,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n  };\n\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    fontSize: theme.typography.fontSize.xs,\n  };\n\n  const handleStyle = {\n    width: '14px',\n    height: '14px',\n    background: 'linear-gradient(135deg, #8C4FFF 0%, #B770FF 100%)',\n    border: `2px solid rgba(255, 255, 255, 0.8)`,\n    borderRadius: '50%',\n    transition: 'all 200ms ease',\n    boxShadow: '0 0 8px rgba(140, 79, 255, 0.6)',\n  };\n\n  return (\n    <div\n      style={defaultStyle}\n      className={`base-node ${className}`}\n      onMouseEnter={(e) => {\n        e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';\n        e.currentTarget.style.boxShadow = `\n          0 16px 32px rgba(140, 79, 255, 0.3),\n          0 0 0 1px rgba(140, 79, 255, 0.4),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `;\n        e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.5)';\n      }}\n      onMouseLeave={(e) => {\n        e.currentTarget.style.transform = 'translateY(0) scale(1)';\n        e.currentTarget.style.boxShadow = `\n          0 8px 16px rgba(140, 79, 255, 0.2),\n          0 0 0 1px rgba(140, 79, 255, 0.1),\n          inset 0 1px 0 rgba(255, 255, 255, 0.1)\n        `;\n        e.currentTarget.style.borderColor = 'rgba(140, 79, 255, 0.3)';\n      }}\n    >\n      {/* Render handles */}\n      {handles.map((handle, index) => (\n        <Handle\n          key={`${id}-${handle.id || index}`}\n          type={handle.type}\n          position={handle.position}\n          id={`${id}-${handle.id}`}\n          style={{\n            ...handleStyle,\n            ...handle.style\n          }}\n        />\n      ))}\n\n      {/* Title */}\n      {title && (\n        <div style={titleStyle}>\n          <span>{title}</span>\n        </div>\n      )}\n\n      {/* Content */}\n      <div style={contentStyle}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Helper function to create handle configurations\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),\n  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),\n  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),\n  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom),\n};\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,WAAW;AAC5C,SAASC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EACvBC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACRC,OAAO,GAAG,EAAE;EACZC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAIC,IAAI,IAAK;IACjC,OAAOf,KAAK,CAACgB,MAAM,CAACC,SAAS,CAACF,IAAI,CAAC,IAAIf,KAAK,CAACgB,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC;EAC9D,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBR,KAAK;IACLC,MAAM;IACNQ,UAAU,EAAE,SAAS;IACrBC,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAEvB,KAAK,CAACwB,OAAO,CAACC,EAAE;IACzBC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,gBAAgB;IAC5BC,UAAU,EAAE,qBAAqB;IACjCC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,SAAS;IAChB,GAAGtB;EACL,CAAC;EAED,MAAMuB,UAAU,GAAG;IACjBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,MAAM;IAChBH,KAAK,EAAE,SAAS;IAChBI,YAAY,EAAEnC,KAAK,CAACwB,OAAO,CAACY,EAAE;IAC9BC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE,mBAAmB;IACjCC,aAAa,EAAEvC,KAAK,CAACwB,OAAO,CAACY,EAAE;IAC/BI,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE;EACjB,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE,CAAC;IACPjB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBiB,cAAc,EAAE,QAAQ;IACxBV,QAAQ,EAAElC,KAAK,CAAC6C,UAAU,CAACX,QAAQ,CAACE;EACtC,CAAC;EAED,MAAMU,WAAW,GAAG;IAClBnC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdQ,UAAU,EAAE,mDAAmD;IAC/DC,MAAM,EAAG,oCAAmC;IAC5CC,YAAY,EAAE,KAAK;IACnBM,UAAU,EAAE,gBAAgB;IAC5BmB,SAAS,EAAE;EACb,CAAC;EAED,oBACE7C,OAAA;IACEO,KAAK,EAAEU,YAAa;IACpBT,SAAS,EAAG,aAAYA,SAAU,EAAE;IACpCsC,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAACzC,KAAK,CAAC0C,SAAS,GAAG,8BAA8B;MAChEF,CAAC,CAACC,aAAa,CAACzC,KAAK,CAACsC,SAAS,GAAI;AAC3C;AACA;AACA;AACA,SAAS;MACDE,CAAC,CAACC,aAAa,CAACzC,KAAK,CAAC2C,WAAW,GAAG,yBAAyB;IAC/D,CAAE;IACFC,YAAY,EAAGJ,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAACzC,KAAK,CAAC0C,SAAS,GAAG,wBAAwB;MAC1DF,CAAC,CAACC,aAAa,CAACzC,KAAK,CAACsC,SAAS,GAAI;AAC3C;AACA;AACA;AACA,SAAS;MACDE,CAAC,CAACC,aAAa,CAACzC,KAAK,CAAC2C,WAAW,GAAG,yBAAyB;IAC/D,CAAE;IAAA7C,QAAA,GAGDC,OAAO,CAAC8C,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBtD,OAAA,CAACJ,MAAM;MAELiB,IAAI,EAAEwC,MAAM,CAACxC,IAAK;MAClBe,QAAQ,EAAEyB,MAAM,CAACzB,QAAS;MAC1B1B,EAAE,EAAG,GAAEA,EAAG,IAAGmD,MAAM,CAACnD,EAAG,EAAE;MACzBK,KAAK,EAAE;QACL,GAAGqC,WAAW;QACd,GAAGS,MAAM,CAAC9C;MACZ;IAAE,GAPI,GAAEL,EAAG,IAAGmD,MAAM,CAACnD,EAAE,IAAIoD,KAAM,EAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQnC,CACF,CAAC,EAGDtD,KAAK,iBACJJ,OAAA;MAAKO,KAAK,EAAEuB,UAAW;MAAAzB,QAAA,eACrBL,OAAA;QAAAK,QAAA,EAAOD;MAAK;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD1D,OAAA;MAAKO,KAAK,EAAEiC,YAAa;MAAAnC,QAAA,EACtBA;IAAQ;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAC,EAAA,GAlHa1D,QAAQ;AAmHrB,OAAO,MAAM2D,YAAY,GAAGA,CAAC1D,EAAE,EAAEW,IAAI,EAAEe,QAAQ,EAAErB,KAAK,GAAG,CAAC,CAAC,MAAM;EAC/DL,EAAE;EACFW,IAAI;EACJe,QAAQ;EACRrB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMsD,cAAc,GAAG;EAC5BC,WAAW,EAAEA,CAAC5D,EAAE,GAAG,QAAQ,KAAK0D,YAAY,CAAC1D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACkE,KAAK,CAAC;EAC1EC,UAAU,EAAEA,CAAC9D,EAAE,GAAG,OAAO,KAAK0D,YAAY,CAAC1D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACoE,IAAI,CAAC;EACvEC,SAAS,EAAEA,CAAChE,EAAE,GAAG,OAAO,KAAK0D,YAAY,CAAC1D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACsE,GAAG,CAAC;EACrEC,YAAY,EAAEA,CAAClE,EAAE,GAAG,QAAQ,KAAK0D,YAAY,CAAC1D,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACwE,MAAM;AAC7E,CAAC;AAAC,IAAAV,EAAA;AAAAW,YAAA,CAAAX,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}