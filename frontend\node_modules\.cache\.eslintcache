[{"D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js": "1", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js": "2", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js": "3", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js": "4", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js": "5", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js": "6", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js": "7", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js": "8", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js": "9", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js": "10", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js": "11", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js": "12", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js": "13", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js": "14", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js": "15", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js": "16", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js": "17", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js": "18", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js": "19", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js": "20"}, {"size": 254, "mtime": 1750274986338, "results": "21", "hashOfConfig": "22"}, {"size": 295, "mtime": 1750279180322, "results": "23", "hashOfConfig": "22"}, {"size": 1960, "mtime": 1750283589421, "results": "24", "hashOfConfig": "22"}, {"size": 5839, "mtime": 1750287058893, "results": "25", "hashOfConfig": "22"}, {"size": 3694, "mtime": 1750285977232, "results": "26", "hashOfConfig": "22"}, {"size": 621, "mtime": 1750285171720, "results": "27", "hashOfConfig": "22"}, {"size": 1353, "mtime": 1750285195771, "results": "28", "hashOfConfig": "22"}, {"size": 1743, "mtime": 1750286620941, "results": "29", "hashOfConfig": "22"}, {"size": 1756, "mtime": 1750286660244, "results": "30", "hashOfConfig": "22"}, {"size": 4856, "mtime": 1750286725841, "results": "31", "hashOfConfig": "22"}, {"size": 744, "mtime": 1750286638437, "results": "32", "hashOfConfig": "22"}, {"size": 3819, "mtime": 1750286535399, "results": "33", "hashOfConfig": "22"}, {"size": 1424, "mtime": 1750279323848, "results": "34", "hashOfConfig": "22"}, {"size": 1539, "mtime": 1750285805588, "results": "35", "hashOfConfig": "22"}, {"size": 1754, "mtime": 1750279303518, "results": "36", "hashOfConfig": "22"}, {"size": 2371, "mtime": 1750285891595, "results": "37", "hashOfConfig": "22"}, {"size": 1542, "mtime": 1750279313385, "results": "38", "hashOfConfig": "22"}, {"size": 2138, "mtime": 1750279121739, "results": "39", "hashOfConfig": "22"}, {"size": 718, "mtime": 1750286504985, "results": "40", "hashOfConfig": "22"}, {"size": 2958, "mtime": 1750285730280, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, "159wf7k", {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js", [], [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js", [], []]