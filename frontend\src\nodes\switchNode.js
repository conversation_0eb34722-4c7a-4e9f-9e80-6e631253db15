// switchNode.js
// Demonstrates conditional routing with multiple outputs

import { useState } from 'react';
import { BaseNode, createHandle } from './BaseNode';
import { Position } from 'reactflow';

export const SwitchNode = ({ id, data }) => {
  const [condition, setCondition] = useState(data?.condition || 'true');

  const handleConditionChange = (e) => {
    setCondition(e.target.value);
  };

  const handles = [
    createHandle('input', 'target', Position.Left),
    createHandle('condition', 'target', Position.Top),
    createHandle('true', 'source', Position.Right, { top: '30%' }),
    createHandle('false', 'source', Position.Right, { top: '70%' })
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Switch"
      handles={handles}
      nodeType="switch"
      height={90}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <label style={{ fontSize: '12px' }}>
          Default:
          <select 
            value={condition} 
            onChange={handleConditionChange}
            style={{ marginLeft: '4px', fontSize: '11px', width: '60px' }}
          >
            <option value="true">True</option>
            <option value="false">False</option>
          </select>
        </label>
        <div style={{ fontSize: '10px', color: '#666' }}>
          Routes based on condition
        </div>
      </div>
    </BaseNode>
  );
};
