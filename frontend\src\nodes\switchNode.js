// switchNode.js
// Demonstrates conditional routing with multiple outputs

import { useState } from 'react';
import { BaseNode, createHandle } from './BaseNode';
import { Position } from 'reactflow';

export const SwitchNode = ({ id, data }) => {
  const [condition, setCondition] = useState(data?.condition || 'true');

  const handleConditionChange = (e) => {
    setCondition(e.target.value);
  };

  const handles = [
    createHandle('input', 'target', Position.Left),
    createHandle('condition', 'target', Position.Top),
    createHandle('true', 'source', Position.Right, { top: '30%' }),
    createHandle('false', 'source', Position.Right, { top: '70%' })
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Switch"
      handles={handles}
      nodeType="switch"
      height={90}
    >
      <div>
        <label className="inline-label">
          Default:
          <select
            value={condition}
            onChange={handleConditionChange}
            className="node-input"
          >
            <option value="true">True</option>
            <option value="false">False</option>
          </select>
        </label>
        <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>
          Routes based on condition
        </div>
      </div>
    </BaseNode>
  );
};
