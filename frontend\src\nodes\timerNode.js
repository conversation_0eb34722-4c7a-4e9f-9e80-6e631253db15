// timerNode.js
// Demonstrates a timer/delay node with configurable duration

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS } from './BaseNode';

export const TimerNode = ({ id, data }) => {
  const [duration, setDuration] = useState(data?.duration || 1000);
  const [unit, setUnit] = useState(data?.unit || 'ms');

  const handleDurationChange = (e) => {
    setDuration(parseInt(e.target.value) || 0);
  };

  const handleUnitChange = (e) => {
    setUnit(e.target.value);
  };

  const handles = [
    HANDLE_CONFIGS.targetLeft('trigger'),
    HANDLE_CONFIGS.sourceRight('delayed')
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Timer"
      handles={handles}
      nodeType="timer"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <label style={{ fontSize: '12px' }}>
          Duration:
          <input 
            type="number" 
            value={duration} 
            onChange={handleDurationChange}
            style={{ marginLeft: '4px', fontSize: '11px', width: '50px' }}
          />
        </label>
        <label style={{ fontSize: '12px' }}>
          Unit:
          <select 
            value={unit} 
            onChange={handleUnitChange}
            style={{ marginLeft: '4px', fontSize: '11px', width: '50px' }}
          >
            <option value="ms">ms</option>
            <option value="s">s</option>
            <option value="m">m</option>
          </select>
        </label>
      </div>
    </BaseNode>
  );
};
