{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\aggregatorNode.js\",\n  _s = $RefreshSig$();\n// aggregatorNode.js\n// Demonstrates data aggregation with multiple inputs and operations\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { NodeInput } from '../components/NodeInput';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AggregatorNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [operation, setOperation] = useState((data === null || data === void 0 ? void 0 : data.operation) || 'concat');\n  const [separator, setSeparator] = useState((data === null || data === void 0 ? void 0 : data.separator) || ', ');\n  const handleOperationChange = e => {\n    setOperation(e.target.value);\n  };\n  const handleSeparatorChange = e => {\n    setSeparator(e.target.value);\n  };\n  const handles = [createHandle('input1', 'target', Position.Left, {\n    top: '20%'\n  }), createHandle('input2', 'target', Position.Left, {\n    top: '40%'\n  }), createHandle('input3', 'target', Position.Left, {\n    top: '60%'\n  }), createHandle('input4', 'target', Position.Left, {\n    top: '80%'\n  }), createHandle('result', 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Aggregator\",\n    handles: handles,\n    nodeType: \"aggregator\",\n    height: 120,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '6px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          fontSize: '11px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '2px'\n        },\n        children: [\"Operation:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: operation,\n          onChange: handleOperationChange,\n          className: \"node-input\",\n          style: {\n            cursor: 'pointer',\n            fontSize: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"concat\",\n            children: \"Concat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"sum\",\n            children: \"Sum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"avg\",\n            children: \"Average\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"max\",\n            children: \"Max\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"min\",\n            children: \"Min\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), operation === 'concat' && /*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          fontSize: '11px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '2px'\n        },\n        children: [\"Separator:\", /*#__PURE__*/_jsxDEV(NodeInput, {\n          value: separator,\n          onChange: handleSeparatorChange,\n          placeholder: \"Enter separator\",\n          style: {\n            fontSize: '10px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '9px',\n          color: 'rgba(255, 255, 255, 0.6)'\n        },\n        children: \"Combines multiple inputs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(AggregatorNode, \"sMY8CXrz1Xg3U6xCkKPfMgyFeGs=\");\n_c = AggregatorNode;\nvar _c;\n$RefreshReg$(_c, \"AggregatorNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "Position", "NodeInput", "jsxDEV", "_jsxDEV", "AggregatorNode", "id", "data", "_s", "operation", "setOperation", "separator", "setSeparator", "handleOperationChange", "e", "target", "value", "handleSeparatorChange", "handles", "Left", "top", "Right", "title", "nodeType", "height", "children", "style", "display", "flexDirection", "gap", "fontSize", "onChange", "className", "cursor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "color", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/aggregatorNode.js"], "sourcesContent": ["// aggregatorNode.js\n// Demonstrates data aggregation with multiple inputs and operations\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { NodeInput } from '../components/NodeInput';\n\nexport const AggregatorNode = ({ id, data }) => {\n  const [operation, setOperation] = useState(data?.operation || 'concat');\n  const [separator, setSeparator] = useState(data?.separator || ', ');\n\n  const handleOperationChange = (e) => {\n    setOperation(e.target.value);\n  };\n\n  const handleSeparatorChange = (e) => {\n    setSeparator(e.target.value);\n  };\n\n  const handles = [\n    createHandle('input1', 'target', Position.Left, { top: '20%' }),\n    createHandle('input2', 'target', Position.Left, { top: '40%' }),\n    createHandle('input3', 'target', Position.Left, { top: '60%' }),\n    createHandle('input4', 'target', Position.Left, { top: '80%' }),\n    createHandle('result', 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Aggregator\"\n      handles={handles}\n      nodeType=\"aggregator\"\n      height={120}\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>\n        <label style={{ fontSize: '11px', display: 'flex', flexDirection: 'column', gap: '2px' }}>\n          Operation:\n          <select\n            value={operation}\n            onChange={handleOperationChange}\n            className=\"node-input\"\n            style={{ cursor: 'pointer', fontSize: '10px' }}\n          >\n            <option value=\"concat\">Concat</option>\n            <option value=\"sum\">Sum</option>\n            <option value=\"avg\">Average</option>\n            <option value=\"max\">Max</option>\n            <option value=\"min\">Min</option>\n          </select>\n        </label>\n        {operation === 'concat' && (\n          <label style={{ fontSize: '11px', display: 'flex', flexDirection: 'column', gap: '2px' }}>\n            Separator:\n            <NodeInput\n              value={separator}\n              onChange={handleSeparatorChange}\n              placeholder=\"Enter separator\"\n              style={{ fontSize: '10px' }}\n            />\n          </label>\n        )}\n        <div style={{ fontSize: '9px', color: 'rgba(255, 255, 255, 0.6)' }}>\n          Combines multiple inputs\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,YAAY;AACnD,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,SAAS,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,QAAQ,CAAC;EACvE,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,KAAI,IAAI,CAAC;EAEnE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,qBAAqB,GAAIH,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAME,OAAO,GAAG,CACdlB,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAEC,QAAQ,CAACkB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EAC/DpB,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAEC,QAAQ,CAACkB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EAC/DpB,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAEC,QAAQ,CAACkB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EAC/DpB,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAEC,QAAQ,CAACkB,IAAI,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EAC/DpB,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAEC,QAAQ,CAACoB,KAAK,CAAC,CACjD;EAED,oBACEjB,OAAA,CAACL,QAAQ;IACPO,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAC,YAAY;IAClBJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,YAAY;IACrBC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAEZrB,OAAA;MAAKsB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnErB,OAAA;QAAOsB,KAAK,EAAE;UAAEI,QAAQ,EAAE,MAAM;UAAEH,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAJ,QAAA,GAAC,YAExF,eAAArB,OAAA;UACEY,KAAK,EAAEP,SAAU;UACjBsB,QAAQ,EAAElB,qBAAsB;UAChCmB,SAAS,EAAC,YAAY;UACtBN,KAAK,EAAE;YAAEO,MAAM,EAAE,SAAS;YAAEH,QAAQ,EAAE;UAAO,CAAE;UAAAL,QAAA,gBAE/CrB,OAAA;YAAQY,KAAK,EAAC,QAAQ;YAAAS,QAAA,EAAC;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCjC,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAS,QAAA,EAAC;UAAG;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCjC,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAS,QAAA,EAAC;UAAO;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCjC,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAS,QAAA,EAAC;UAAG;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCjC,OAAA;YAAQY,KAAK,EAAC,KAAK;YAAAS,QAAA,EAAC;UAAG;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACP5B,SAAS,KAAK,QAAQ,iBACrBL,OAAA;QAAOsB,KAAK,EAAE;UAAEI,QAAQ,EAAE,MAAM;UAAEH,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAJ,QAAA,GAAC,YAExF,eAAArB,OAAA,CAACF,SAAS;UACRc,KAAK,EAAEL,SAAU;UACjBoB,QAAQ,EAAEd,qBAAsB;UAChCqB,WAAW,EAAC,iBAAiB;UAC7BZ,KAAK,EAAE;YAAEI,QAAQ,EAAE;UAAO;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR,eACDjC,OAAA;QAAKsB,KAAK,EAAE;UAAEI,QAAQ,EAAE,KAAK;UAAES,KAAK,EAAE;QAA2B,CAAE;QAAAd,QAAA,EAAC;MAEpE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAC7B,EAAA,CA9DWH,cAAc;AAAAmC,EAAA,GAAdnC,cAAc;AAAA,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}