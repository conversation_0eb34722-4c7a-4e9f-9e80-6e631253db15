{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\submit.js\",\n  _s = $RefreshSig$();\n// submit.js\n\nimport { useStore } from './store';\nimport { shallow } from 'zustand/shallow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst selector = state => ({\n  nodes: state.nodes,\n  edges: state.edges\n});\nexport const SubmitButton = () => {\n  _s();\n  const {\n    nodes,\n    edges\n  } = useStore(selector, shallow);\n  const handleSubmit = async () => {\n    try {\n      // Prepare pipeline data\n      const pipelineData = {\n        nodes: nodes,\n        edges: edges\n      };\n\n      // Send to backend\n      const response = await fetch('http://localhost:8000/pipelines/parse', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(pipelineData)\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n\n      // Create user-friendly alert message\n      const dagStatus = result.is_dag ? \"✅ Valid DAG\" : \"❌ Contains Cycles\";\n      const message = `Pipeline Analysis Results:\n\n📊 Number of Nodes: ${result.num_nodes}\n🔗 Number of Edges: ${result.num_edges}\n🔄 DAG Status: ${dagStatus}\n\n${result.is_dag ? \"Great! Your pipeline is a valid Directed Acyclic Graph.\" : \"Warning: Your pipeline contains cycles and is not a valid DAG.\"}`;\n      alert(message);\n    } catch (error) {\n      console.error('Error submitting pipeline:', error);\n      alert(`Error submitting pipeline: ${error.message}`);\n    }\n  };\n  const hasNodes = nodes.length > 0;\n  const hasConnections = edges.length > 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"submit-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"submit-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pipeline-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: nodes.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Nodes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-divider\",\n          children: \"\\u2022\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: edges.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Connections\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this), !hasNodes && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"getting-started\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"start-hint\",\n          children: \"\\uD83D\\uDC46 Drag nodes from above to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"button\",\n      className: `submit-button ${!hasNodes ? 'disabled' : ''}`,\n      onClick: handleSubmit,\n      disabled: !hasNodes,\n      children: hasNodes ? 'Analyze Pipeline' : 'Build Your Pipeline'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 9\n  }, this);\n};\n_s(SubmitButton, \"jhO/6NzdkdwyKxfTPfR/ATfN7AQ=\", false, function () {\n  return [useStore];\n});\n_c = SubmitButton;\nvar _c;\n$RefreshReg$(_c, \"SubmitButton\");", "map": {"version": 3, "names": ["useStore", "shallow", "jsxDEV", "_jsxDEV", "selector", "state", "nodes", "edges", "SubmitButton", "_s", "handleSubmit", "pipelineData", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "status", "result", "json", "dagStatus", "is_dag", "message", "num_nodes", "num_edges", "alert", "error", "console", "hasNodes", "length", "hasConnections", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/submit.js"], "sourcesContent": ["// submit.js\n\nimport { useStore } from './store';\nimport { shallow } from 'zustand/shallow';\n\nconst selector = (state) => ({\n  nodes: state.nodes,\n  edges: state.edges,\n});\n\nexport const SubmitButton = () => {\n    const { nodes, edges } = useStore(selector, shallow);\n\n    const handleSubmit = async () => {\n        try {\n            // Prepare pipeline data\n            const pipelineData = {\n                nodes: nodes,\n                edges: edges\n            };\n\n            // Send to backend\n            const response = await fetch('http://localhost:8000/pipelines/parse', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify(pipelineData)\n            });\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const result = await response.json();\n\n            // Create user-friendly alert message\n            const dagStatus = result.is_dag ? \"✅ Valid DAG\" : \"❌ Contains Cycles\";\n            const message = `Pipeline Analysis Results:\n\n📊 Number of Nodes: ${result.num_nodes}\n🔗 Number of Edges: ${result.num_edges}\n🔄 DAG Status: ${dagStatus}\n\n${result.is_dag ?\n    \"Great! Your pipeline is a valid Directed Acyclic Graph.\" :\n    \"Warning: Your pipeline contains cycles and is not a valid DAG.\"}`;\n\n            alert(message);\n\n        } catch (error) {\n            console.error('Error submitting pipeline:', error);\n            alert(`Error submitting pipeline: ${error.message}`);\n        }\n    };\n\n    const hasNodes = nodes.length > 0;\n    const hasConnections = edges.length > 0;\n\n    return (\n        <div className=\"submit-container\">\n            <div className=\"submit-info\">\n                <div className=\"pipeline-stats\">\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{nodes.length}</span>\n                        <span className=\"stat-label\">Nodes</span>\n                    </span>\n                    <span className=\"stat-divider\">•</span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{edges.length}</span>\n                        <span className=\"stat-label\">Connections</span>\n                    </span>\n                </div>\n                {!hasNodes && (\n                    <div className=\"getting-started\">\n                        <span className=\"start-hint\">👆 Drag nodes from above to get started</span>\n                    </div>\n                )}\n            </div>\n            <button\n                type=\"button\"\n                className={`submit-button ${!hasNodes ? 'disabled' : ''}`}\n                onClick={handleSubmit}\n                disabled={!hasNodes}\n            >\n                {hasNodes ? 'Analyze Pipeline' : 'Build Your Pipeline'}\n            </button>\n        </div>\n    );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,SAAS;AAClC,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,QAAQ,GAAIC,KAAK,KAAM;EAC3BC,KAAK,EAAED,KAAK,CAACC,KAAK;EAClBC,KAAK,EAAEF,KAAK,CAACE;AACf,CAAC,CAAC;AAEF,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEH,KAAK;IAAEC;EAAM,CAAC,GAAGP,QAAQ,CAACI,QAAQ,EAAEH,OAAO,CAAC;EAEpD,MAAMS,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA;MACA,MAAMC,YAAY,GAAG;QACjBL,KAAK,EAAEA,KAAK;QACZC,KAAK,EAAEA;MACX,CAAC;;MAED;MACA,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACL,cAAc,EAAE;QACpB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,YAAY;MACrC,CAAC,CAAC;MAEF,IAAI,CAACC,QAAQ,CAACO,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAE,uBAAsBR,QAAQ,CAACS,MAAO,EAAC,CAAC;MAC7D;MAEA,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;;MAEpC;MACA,MAAMC,SAAS,GAAGF,MAAM,CAACG,MAAM,GAAG,aAAa,GAAG,mBAAmB;MACrE,MAAMC,OAAO,GAAI;AAC7B;AACA,sBAAsBJ,MAAM,CAACK,SAAU;AACvC,sBAAsBL,MAAM,CAACM,SAAU;AACvC,iBAAiBJ,SAAU;AAC3B;AACA,EAAEF,MAAM,CAACG,MAAM,GACX,yDAAyD,GACzD,gEAAiE,EAAC;MAE1DI,KAAK,CAACH,OAAO,CAAC;IAElB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDD,KAAK,CAAE,8BAA6BC,KAAK,CAACJ,OAAQ,EAAC,CAAC;IACxD;EACJ,CAAC;EAED,MAAMM,QAAQ,GAAG1B,KAAK,CAAC2B,MAAM,GAAG,CAAC;EACjC,MAAMC,cAAc,GAAG3B,KAAK,CAAC0B,MAAM,GAAG,CAAC;EAEvC,oBACI9B,OAAA;IAAKgC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC7BjC,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBjC,OAAA;QAAKgC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BjC,OAAA;UAAMgC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACvBjC,OAAA;YAAMgC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE9B,KAAK,CAAC2B;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDrC,OAAA;YAAMgC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACPrC,OAAA;UAAMgC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCrC,OAAA;UAAMgC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACvBjC,OAAA;YAAMgC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE7B,KAAK,CAAC0B;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDrC,OAAA;YAAMgC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EACL,CAACR,QAAQ,iBACN7B,OAAA;QAAKgC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BjC,OAAA;UAAMgC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACNrC,OAAA;MACIsC,IAAI,EAAC,QAAQ;MACbN,SAAS,EAAG,iBAAgB,CAACH,QAAQ,GAAG,UAAU,GAAG,EAAG,EAAE;MAC1DU,OAAO,EAAEhC,YAAa;MACtBiC,QAAQ,EAAE,CAACX,QAAS;MAAAI,QAAA,EAEnBJ,QAAQ,GAAG,kBAAkB,GAAG;IAAqB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAA/B,EAAA,CA/EYD,YAAY;EAAA,QACIR,QAAQ;AAAA;AAAA4C,EAAA,GADxBpC,YAAY;AAAA,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}