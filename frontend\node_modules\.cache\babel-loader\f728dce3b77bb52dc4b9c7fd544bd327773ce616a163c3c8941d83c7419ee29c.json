{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\components\\\\NodeInput.js\";\n// NodeInput.js\n// Reusable input component for node properties with overflow handling and tooltips\n\nimport { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const NodeInput = ({\n  value,\n  onChange,\n  placeholder = \"\",\n  label = \"\",\n  showTooltip = true,\n  style = {},\n  ...props\n}) => {\n  const shouldShowTooltip = showTooltip && value && value.length > 15;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"input-container\",\n    children: [shouldShowTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"input-tooltip\",\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      value: value,\n      onChange: onChange,\n      placeholder: placeholder,\n      className: \"node-input\",\n      onFocus: () => setIsFocused(true),\n      onBlur: () => setIsFocused(false),\n      style: style,\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_c = NodeInput;\nvar _c;\n$RefreshReg$(_c, \"NodeInput\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "NodeInput", "value", "onChange", "placeholder", "label", "showTooltip", "style", "props", "shouldShowTooltip", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onFocus", "setIsFocused", "onBlur", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/components/NodeInput.js"], "sourcesContent": ["// NodeInput.js\n// Reusable input component for node properties with overflow handling and tooltips\n\nimport { useState } from 'react';\n\nexport const NodeInput = ({\n  value,\n  onChange,\n  placeholder = \"\",\n  label = \"\",\n  showTooltip = true,\n  style = {},\n  ...props\n}) => {\n  const shouldShowTooltip = showTooltip && value && value.length > 15;\n\n  return (\n    <div className=\"input-container\">\n      {shouldShowTooltip && (\n        <div className=\"input-tooltip\">\n          {value}\n        </div>\n      )}\n      <input\n        type=\"text\"\n        value={value}\n        onChange={onChange}\n        placeholder={placeholder}\n        className=\"node-input\"\n        onFocus={() => setIsFocused(true)}\n        onBlur={() => setIsFocused(false)}\n        style={style}\n        {...props}\n      />\n    </div>\n  );\n};\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,OAAO,MAAMC,SAAS,GAAGA,CAAC;EACxBC,KAAK;EACLC,QAAQ;EACRC,WAAW,GAAG,EAAE;EAChBC,KAAK,GAAG,EAAE;EACVC,WAAW,GAAG,IAAI;EAClBC,KAAK,GAAG,CAAC,CAAC;EACV,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,iBAAiB,GAAGH,WAAW,IAAIJ,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,EAAE;EAEnE,oBACEV,OAAA;IAAKW,SAAS,EAAC,iBAAiB;IAAAC,QAAA,GAC7BH,iBAAiB,iBAChBT,OAAA;MAAKW,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BV;IAAK;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eACDhB,OAAA;MACEiB,IAAI,EAAC,MAAM;MACXf,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAEA,QAAS;MACnBC,WAAW,EAAEA,WAAY;MACzBO,SAAS,EAAC,YAAY;MACtBO,OAAO,EAAEA,CAAA,KAAMC,YAAY,CAAC,IAAI,CAAE;MAClCC,MAAM,EAAEA,CAAA,KAAMD,YAAY,CAAC,KAAK,CAAE;MAClCZ,KAAK,EAAEA,KAAM;MAAA,GACTC;IAAK;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACK,EAAA,GA/BWpB,SAAS;AAAA,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}