// inputNode.js

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS } from './BaseNode';

export const InputNode = ({ id, data }) => {
  const [currName, setCurrName] = useState(data?.inputName || id.replace('customInput-', 'input_'));
  const [inputType, setInputType] = useState(data.inputType || 'Text');

  const handleNameChange = (e) => {
    setCurrName(e.target.value);
  };

  const handleTypeChange = (e) => {
    setInputType(e.target.value);
  };

  const handles = [HANDLE_CONFIGS.sourceRight('value')];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Input"
      handles={handles}
      nodeType="input"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <label style={{ fontSize: '12px' }}>
          Name:
          <input
            type="text"
            value={currName}
            onChange={handleNameChange}
            style={{ marginLeft: '4px', fontSize: '11px', width: '100px' }}
          />
        </label>
        <label style={{ fontSize: '12px' }}>
          Type:
          <select
            value={inputType}
            onChange={handleTypeChange}
            style={{ marginLeft: '4px', fontSize: '11px', width: '100px' }}
          >
            <option value="Text">Text</option>
            <option value="File">File</option>
          </select>
        </label>
      </div>
    </BaseNode>
  );
}
