// inputNode.js

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS } from './BaseNode';
import { NodeInput } from '../components/NodeInput';

export const InputNode = ({ id, data }) => {
  const [currName, setCurrName] = useState(data?.inputName || id.replace('customInput-', 'input_'));
  const [inputType, setInputType] = useState(data.inputType || 'Text');

  const handleNameChange = (e) => {
    setCurrName(e.target.value);
  };

  const handleTypeChange = (e) => {
    setInputType(e.target.value);
  };

  const handles = [HANDLE_CONFIGS.sourceRight('value')];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Input"
      handles={handles}
      nodeType="input"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={{
          fontSize: '12px',
          display: 'flex',
          flexDirection: 'column',
          gap: '4px',
          color: '#ffffff',
          fontWeight: '600'
        }}>
          Name:
          <NodeInput
            value={currName}
            onChange={handleNameChange}
            placeholder="Enter input name"
          />
        </label>
        <label style={{
          fontSize: '12px',
          display: 'flex',
          flexDirection: 'column',
          gap: '4px',
          color: '#ffffff',
          fontWeight: '600'
        }}>
          Type:
          <select
            value={inputType}
            onChange={handleTypeChange}
            className="node-input"
            style={{ cursor: 'pointer' }}
          >
            <option value="Text">Text</option>
            <option value="File">File</option>
          </select>
        </label>
      </div>
    </BaseNode>
  );
}
