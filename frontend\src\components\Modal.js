// Modal.js
// Modern modal component with VectorShift styling

import { useEffect } from 'react';

export const Modal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  showCloseButton = true 
}) => {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-container">
        <div className="modal-header">
          <h2 className="modal-title">{title}</h2>
          {showCloseButton && (
            <button 
              className="modal-close" 
              onClick={onClose}
              aria-label="Close modal"
            >
              ×
            </button>
          )}
        </div>
        <div className="modal-content">
          {children}
        </div>
      </div>
    </div>
  );
};

export const PipelineAnalysisModal = ({ 
  isOpen, 
  onClose, 
  analysisResult 
}) => {
  if (!analysisResult) return null;

  const { num_nodes, num_edges, is_dag } = analysisResult;
  const dagStatus = is_dag ? "✅ Valid DAG" : "❌ Contains Cycles";

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={
        <span>
          📊 Pipeline Analysis Results
        </span>
      }
    >
      <div className="modal-section">
        <div className="modal-stat">
          <span className="modal-stat-icon">📦</span>
          <span className="modal-stat-text">Number of Nodes: {num_nodes}</span>
        </div>
        
        <div className="modal-stat">
          <span className="modal-stat-icon">🔗</span>
          <span className="modal-stat-text">Number of Edges: {num_edges}</span>
        </div>
        
        <div className="modal-stat">
          <span className="modal-stat-icon">🔄</span>
          <span className="modal-stat-text">DAG Status: {dagStatus}</span>
        </div>
      </div>

      <div className={`modal-status ${is_dag ? 'success' : 'error'}`}>
        <span>{is_dag ? '✅' : '⚠️'}</span>
        <span>
          {is_dag 
            ? "Great! Your pipeline is a valid Directed Acyclic Graph." 
            : "Warning: Your pipeline contains cycles and is not a valid DAG."
          }
        </span>
      </div>

      <div className="modal-actions">
        <button 
          className="modal-button primary" 
          onClick={onClose}
        >
          Got it!
        </button>
      </div>
    </Modal>
  );
};
