// toolbar.js

import { DraggableNode } from './draggableNode';

export const PipelineToolbar = () => {

    return (
        <div className="pipeline-toolbar">
            <div className="toolbar-header">
                <div className="toolbar-brand">
                    <div className="brand-logo">
                        <svg width="32" height="32" viewBox="0 0 40 40" className="logo-svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stopColor="#8b5cf6" />
                                    <stop offset="100%" stopColor="#6366f1" />
                                </linearGradient>
                            </defs>
                            <path d="M8 32L20 8L32 32H8Z" fill="url(#logoGradient)" />
                            <circle cx="20" cy="20" r="3" fill="white" />
                        </svg>
                    </div>
                    <div className="brand-text">
                        <div className="toolbar-title">
                            VectorShift Pipeline Builder
                        </div>
                    </div>
                </div>

                <div className="toolbar-nodes">
                    <DraggableNode type='customInput' label='Input' />
                    <DraggableNode type='llm' label='LLM' />
                    <DraggableNode type='text' label='Text' />
                    <DraggableNode type='customOutput' label='Output' />
                    <DraggableNode type='math' label='Math' />
                    <DraggableNode type='filter' label='Filter' />
                    <DraggableNode type='aggregator' label='Aggregator' />
                    <DraggableNode type='switch' label='Switch' />
                    <DraggableNode type='timer' label='Timer' />
                </div>
            </div>
        </div>
    );
};
