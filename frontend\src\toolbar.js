// toolbar.js

import { DraggableNode } from './draggableNode';

export const PipelineToolbar = () => {

    return (
        <div className="pipeline-toolbar">
            <div className="toolbar-title">
                VectorShift Pipeline Builder
            </div>
            <div className="toolbar-subtitle">
                Drag nodes to canvas • Connect workflows • Build the future
            </div>
            <div className="toolbar-nodes">
                {/* Core Nodes */}
                <DraggableNode type='customInput' label='Input' />
                <DraggableNode type='llm' label='LLM' />
                <DraggableNode type='customOutput' label='Output' />
                <DraggableNode type='text' label='Text' />

                {/* Advanced Nodes */}
                <DraggableNode type='math' label='Math' />
                <DraggableNode type='filter' label='Filter' />
                <DraggableNode type='timer' label='Timer' />
                <DraggableNode type='switch' label='Switch' />
                <DraggableNode type='aggregator' label='Aggregator' />
            </div>
        </div>
    );
};
