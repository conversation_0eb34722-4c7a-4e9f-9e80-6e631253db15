from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import json

app = FastAPI()

# Add CORS middleware to allow frontend requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class PipelineData(BaseModel):
    nodes: List[Dict[str, Any]]
    edges: List[Dict[str, Any]]

@app.get('/')
def read_root():
    return {'Ping': 'Pong'}

def is_dag(nodes: List[Dict], edges: List[Dict]) -> bool:
    """
    Check if the graph formed by nodes and edges is a Directed Acyclic Graph (DAG).
    Uses DFS with color coding: white (0), gray (1), black (2).
    """
    if not nodes or not edges:
        return True

    # Build adjacency list
    graph = {}
    node_ids = {node['id'] for node in nodes}

    # Initialize all nodes in graph
    for node_id in node_ids:
        graph[node_id] = []

    # Add edges to graph
    for edge in edges:
        source = edge.get('source')
        target = edge.get('target')

        if source in node_ids and target in node_ids:
            graph[source].append(target)

    # DFS with color coding to detect cycles
    color = {node_id: 0 for node_id in node_ids}  # 0: white, 1: gray, 2: black

    def dfs(node):
        if color[node] == 1:  # Gray node - back edge found (cycle)
            return False
        if color[node] == 2:  # Black node - already processed
            return True

        color[node] = 1  # Mark as gray (visiting)

        for neighbor in graph[node]:
            if not dfs(neighbor):
                return False

        color[node] = 2  # Mark as black (finished)
        return True

    # Check all nodes for cycles
    for node_id in node_ids:
        if color[node_id] == 0:  # White node - not visited
            if not dfs(node_id):
                return False

    return True

@app.post('/pipelines/parse')
def parse_pipeline(pipeline_data: PipelineData):
    """
    Parse pipeline data and return analysis including DAG validation.
    """
    try:
        nodes = pipeline_data.nodes
        edges = pipeline_data.edges

        num_nodes = len(nodes)
        num_edges = len(edges)
        is_dag_result = is_dag(nodes, edges)

        return {
            'num_nodes': num_nodes,
            'num_edges': num_edges,
            'is_dag': is_dag_result
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error parsing pipeline: {str(e)}")
