// ui.js
// Displays the drag-and-drop UI
// --------------------------------------------------

import { useState, useRef, useCallback } from 'react';
import ReactFlow, { Controls, Background, MiniMap } from 'reactflow';
import { useStore } from './store';
import { shallow } from 'zustand/shallow';
import { InputNode } from './nodes/inputNode';
import { LLMNode } from './nodes/llmNode';
import { OutputNode } from './nodes/outputNode';
import { TextNode } from './nodes/textNode';
import { MathNode } from './nodes/mathNode';
import { FilterNode } from './nodes/filterNode';
import { TimerNode } from './nodes/timerNode';
import { SwitchNode } from './nodes/switchNode';
import { AggregatorNode } from './nodes/aggregatorNode';

import 'reactflow/dist/style.css';

const gridSize = 20;
const proOptions = { hideAttribution: true };
const nodeTypes = {
  customInput: InputNode,
  llm: LLMNode,
  customOutput: OutputNode,
  text: TextNode,
  math: MathNode,
  filter: FilterNode,
  timer: TimerNode,
  switch: SwitchNode,
  aggregator: AggregatorNode,
};

const selector = (state) => ({
  nodes: state.nodes,
  edges: state.edges,
  getNodeID: state.getNodeID,
  addNode: state.addNode,
  onNodesChange: state.onNodesChange,
  onEdgesChange: state.onEdgesChange,
  onConnect: state.onConnect,
});

export const PipelineUI = () => {
    const reactFlowWrapper = useRef(null);
    const [reactFlowInstance, setReactFlowInstance] = useState(null);
    const {
      nodes,
      edges,
      getNodeID,
      addNode,
      onNodesChange,
      onEdgesChange,
      onConnect
    } = useStore(selector, shallow);

    const getInitNodeData = (nodeID, type) => {
      let nodeData = { id: nodeID, nodeType: `${type}` };
      return nodeData;
    }

    const onDrop = useCallback(
        (event) => {
          event.preventDefault();

          const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
          const dragData = event?.dataTransfer?.getData('application/reactflow');

          if (dragData) {
            const appData = JSON.parse(dragData);
            const type = appData?.nodeType;

            // check if the dropped element is valid
            if (typeof type === 'undefined' || !type) {
              return;
            }

            if (!reactFlowInstance) {
              return;
            }

            const position = reactFlowInstance.project({
              x: event.clientX - reactFlowBounds.left,
              y: event.clientY - reactFlowBounds.top,
            });

            const nodeID = getNodeID(type);
            const newNode = {
              id: nodeID,
              type,
              position,
              data: getInitNodeData(nodeID, type),
            };

            addNode(newNode);
          }
        },
        [reactFlowInstance, addNode, getNodeID]
    );

    const onDragOver = useCallback((event) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
    }, []);

    return (
        <div className="pipeline-ui-container">
            <div
                ref={reactFlowWrapper}
                style={{
                    width: '100%',
                    height: '100%',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0
                }}
            >
                <ReactFlow
                    nodes={nodes}
                    edges={edges}
                    onNodesChange={onNodesChange}
                    onEdgesChange={onEdgesChange}
                    onConnect={onConnect}
                    onDrop={onDrop}
                    onDragOver={onDragOver}
                    onInit={setReactFlowInstance}
                    nodeTypes={nodeTypes}
                    proOptions={proOptions}
                    snapGrid={[gridSize, gridSize]}
                    connectionLineType='smoothstep'
                    style={{
                        background: 'transparent',
                        width: '100%',
                        height: '100%'
                    }}
                    fitView
                >
                    <Background
                        variant="dots"
                        gap={30}
                        size={3}
                        color="#a855f7"
                        style={{ opacity: 0.6 }}
                    />
                    <Controls
                        style={{
                            button: {
                                backgroundColor: 'rgba(26, 11, 46, 0.9)',
                                color: '#ffffff',
                                border: '1px solid rgba(138, 43, 226, 0.4)',
                            }
                        }}
                    />
                    <MiniMap
                        nodeColor={(node) => {
                            const colors = {
                                customInput: 'rgba(140, 79, 255, 0.6)',
                                llm: 'rgba(183, 112, 255, 0.6)',
                                customOutput: 'rgba(140, 79, 255, 0.6)',
                                text: 'rgba(183, 112, 255, 0.6)',
                                math: 'rgba(140, 79, 255, 0.6)',
                                filter: 'rgba(183, 112, 255, 0.6)',
                                timer: 'rgba(140, 79, 255, 0.6)',
                                switch: 'rgba(183, 112, 255, 0.6)',
                                aggregator: 'rgba(140, 79, 255, 0.6)'
                            };
                            return colors[node.type] || 'rgba(140, 79, 255, 0.4)';
                        }}
                        maskColor="rgba(15, 12, 26, 0.8)"
                    />
                </ReactFlow>
            </div>
        </div>
    )
}
