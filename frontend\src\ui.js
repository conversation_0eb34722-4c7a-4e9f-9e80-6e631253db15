// ui.js
// Displays the drag-and-drop UI
// --------------------------------------------------

import { useState, useRef, useCallback } from 'react';
import ReactFlow, { Controls, Background, MiniMap } from 'reactflow';
import { useStore } from './store';
import { shallow } from 'zustand/shallow';
import { InputNode } from './nodes/inputNode';
import { LLMNode } from './nodes/llmNode';
import { OutputNode } from './nodes/outputNode';
import { TextNode } from './nodes/textNode';
import { MathNode } from './nodes/mathNode';
import { FilterNode } from './nodes/filterNode';
import { TimerNode } from './nodes/timerNode';
import { SwitchNode } from './nodes/switchNode';
import { AggregatorNode } from './nodes/aggregatorNode';

import 'reactflow/dist/style.css';

const gridSize = 20;
const proOptions = { hideAttribution: true };
const nodeTypes = {
  customInput: InputNode,
  llm: LLMNode,
  customOutput: OutputNode,
  text: TextNode,
  math: MathNode,
  filter: FilterNode,
  timer: TimerNode,
  switch: SwitchNode,
  aggregator: AggregatorNode,
};

const selector = (state) => ({
  nodes: state.nodes,
  edges: state.edges,
  getNodeID: state.getNodeID,
  addNode: state.addNode,
  onNodesChange: state.onNodesChange,
  onEdgesChange: state.onEdgesChange,
  onConnect: state.onConnect,
});

export const PipelineUI = () => {
    const reactFlowWrapper = useRef(null);
    const [reactFlowInstance, setReactFlowInstance] = useState(null);
    const {
      nodes,
      edges,
      getNodeID,
      addNode,
      onNodesChange,
      onEdgesChange,
      onConnect
    } = useStore(selector, shallow);

    const getInitNodeData = (nodeID, type) => {
      let nodeData = { id: nodeID, nodeType: `${type}` };
      return nodeData;
    }

    const onDrop = useCallback(
        (event) => {
          event.preventDefault();
    
          const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
          if (event?.dataTransfer?.getData('application/reactflow')) {
            const appData = JSON.parse(event.dataTransfer.getData('application/reactflow'));
            const type = appData?.nodeType;
      
            // check if the dropped element is valid
            if (typeof type === 'undefined' || !type) {
              return;
            }
      
            const position = reactFlowInstance.project({
              x: event.clientX - reactFlowBounds.left,
              y: event.clientY - reactFlowBounds.top,
            });

            const nodeID = getNodeID(type);
            const newNode = {
              id: nodeID,
              type,
              position,
              data: getInitNodeData(nodeID, type),
            };
      
            addNode(newNode);
          }
        },
        [reactFlowInstance, addNode, getNodeID]
    );

    const onDragOver = useCallback((event) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
    }, []);

    return (
        <div className="pipeline-ui-container">
            <div ref={reactFlowWrapper} style={{width: '100%', height: '100%'}}>
                <ReactFlow
                    nodes={nodes}
                    edges={edges}
                    onNodesChange={onNodesChange}
                    onEdgesChange={onEdgesChange}
                    onConnect={onConnect}
                    onDrop={onDrop}
                    onDragOver={onDragOver}
                    onInit={setReactFlowInstance}
                    nodeTypes={nodeTypes}
                    proOptions={proOptions}
                    snapGrid={[gridSize, gridSize]}
                    connectionLineType='smoothstep'
                    style={{ background: 'transparent' }}
                >
                    <Background
                        color="rgba(255, 255, 255, 0.1)"
                        gap={gridSize}
                        style={{ opacity: 0.5 }}
                    />
                    <Controls
                        style={{
                            background: 'rgba(255, 255, 255, 0.9)',
                            borderRadius: '8px',
                            border: '1px solid rgba(229, 231, 235, 0.8)'
                        }}
                    />
                    <MiniMap
                        style={{
                            background: 'rgba(255, 255, 255, 0.9)',
                            borderRadius: '8px',
                            border: '1px solid rgba(229, 231, 235, 0.8)'
                        }}
                        nodeColor={(node) => {
                            const colors = {
                                customInput: '#e3f2fd',
                                llm: '#fff3e0',
                                customOutput: '#f3e5f5',
                                text: '#e8f5e8',
                                math: '#e3f2fd',
                                filter: '#fff3e0',
                                timer: '#f3e5f5',
                                switch: '#e8f5e8',
                                aggregator: '#fce4ec'
                            };
                            return colors[node.type] || '#f9fafb';
                        }}
                    />
                </ReactFlow>
            </div>
        </div>
    )
}
