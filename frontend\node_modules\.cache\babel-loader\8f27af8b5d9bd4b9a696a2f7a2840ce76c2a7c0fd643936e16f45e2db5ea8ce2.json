{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\draggableNode.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// draggableNode.js\n\nexport const DraggableNode = ({\n  type,\n  label\n}) => {\n  const onDragStart = (event, nodeType) => {\n    const appData = {\n      nodeType\n    };\n    console.log('Drag start:', nodeType, appData);\n    event.target.style.cursor = 'grabbing';\n    event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n    event.dataTransfer.effectAllowed = 'move';\n    console.log('Data set in dataTransfer');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `draggable-node ${type}`,\n    onDragStart: event => onDragStart(event, type),\n    onDragEnd: event => event.target.style.cursor = 'grab',\n    draggable: true,\n    children: /*#__PURE__*/_jsxDEV(\"span\", {\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 11\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 7\n  }, this);\n};\n_c = DraggableNode;\nvar _c;\n$RefreshReg$(_c, \"DraggableNode\");", "map": {"version": 3, "names": ["DraggableNode", "type", "label", "onDragStart", "event", "nodeType", "appData", "console", "log", "target", "style", "cursor", "dataTransfer", "setData", "JSON", "stringify", "effectAllowed", "_jsxDEV", "className", "onDragEnd", "draggable", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/draggableNode.js"], "sourcesContent": ["// draggableNode.js\n\nexport const DraggableNode = ({ type, label }) => {\n    const onDragStart = (event, nodeType) => {\n      const appData = { nodeType }\n      console.log('Drag start:', nodeType, appData);\n      event.target.style.cursor = 'grabbing';\n      event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n      event.dataTransfer.effectAllowed = 'move';\n      console.log('Data set in dataTransfer');\n    };\n\n    return (\n      <div\n        className={`draggable-node ${type}`}\n        onDragStart={(event) => onDragStart(event, type)}\n        onDragEnd={(event) => (event.target.style.cursor = 'grab')}\n        draggable\n      >\n          <span>{label}</span>\n      </div>\n    );\n  };\n  "], "mappings": ";;AAAA;;AAEA,OAAO,MAAMA,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAC9C,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IACvC,MAAMC,OAAO,GAAG;MAAED;IAAS,CAAC;IAC5BE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEH,QAAQ,EAAEC,OAAO,CAAC;IAC7CF,KAAK,CAACK,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,UAAU;IACtCP,KAAK,CAACQ,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAACT,OAAO,CAAC,CAAC;IAC5EF,KAAK,CAACQ,YAAY,CAACI,aAAa,GAAG,MAAM;IACzCT,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC,CAAC;EAED,oBACES,OAAA;IACEC,SAAS,EAAG,kBAAiBjB,IAAK,EAAE;IACpCE,WAAW,EAAGC,KAAK,IAAKD,WAAW,CAACC,KAAK,EAAEH,IAAI,CAAE;IACjDkB,SAAS,EAAGf,KAAK,IAAMA,KAAK,CAACK,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,MAAQ;IAC3DS,SAAS;IAAAC,QAAA,eAEPJ,OAAA;MAAAI,QAAA,EAAOnB;IAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEV,CAAC;AAACC,EAAA,GApBS1B,aAAa;AAAA,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}