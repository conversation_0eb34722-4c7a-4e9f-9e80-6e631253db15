// llmNode.js

import { BaseNode, createHandle } from './BaseNode';
import { Position } from 'reactflow';

export const LLMNode = ({ id, data }) => {
  const handles = [
    createHandle('system', 'target', Position.Left, { top: `${100/3}%` }),
    createHandle('prompt', 'target', Position.Left, { top: `${200/3}%` }),
    createHandle('response', 'source', Position.Right)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="LLM"
      handles={handles}
      nodeType="llm"
    >
      <div style={{ fontSize: '12px', color: '#666' }}>
        <span>This is a LLM.</span>
      </div>
    </BaseNode>
  );
}
