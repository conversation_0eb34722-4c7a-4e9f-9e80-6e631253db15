{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\BaseNode.js\";\n// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 220,\n  height = 100,\n  nodeType = 'default'\n}) => {\n  const getNodeTypeColor = type => {\n    return theme.colors.nodeTypes[type] || theme.colors.gray[50];\n  };\n  const defaultStyle = {\n    width,\n    height,\n    background: `linear-gradient(135deg, rgba(140, 79, 255, 0.15) 0%, rgba(183, 112, 255, 0.1) 100%)`,\n    backdropFilter: 'blur(10px)',\n    border: `2px solid rgba(140, 79, 255, 0.3)`,\n    borderRadius: '16px',\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    boxShadow: `\n      0 8px 16px rgba(140, 79, 255, 0.2),\n      0 0 0 1px rgba(140, 79, 255, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.1)\n    `,\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n    position: 'relative',\n    overflow: 'hidden',\n    color: '#FFFFFF',\n    ...style\n  };\n  const titleStyle = {\n    fontWeight: theme.typography.fontWeight.semibold,\n    fontSize: theme.typography.fontSize.sm,\n    color: theme.colors.gray[700],\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: `1px solid ${theme.colors.gray[200]}`,\n    paddingBottom: theme.spacing.xs\n  };\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    fontSize: theme.typography.fontSize.xs\n  };\n  const handleStyle = {\n    width: '12px',\n    height: '12px',\n    background: theme.colors.primary[500],\n    border: `2px solid ${theme.colors.white}`,\n    borderRadius: '50%',\n    transition: theme.transitions.fast\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: defaultStyle,\n    className: `base-node ${className}`,\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-2px)';\n      e.currentTarget.style.boxShadow = theme.shadows.lg;\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0)';\n      e.currentTarget.style.boxShadow = theme.shadows.md;\n    },\n    children: [handles.map((handle, index) => /*#__PURE__*/_jsxDEV(Handle, {\n      type: handle.type,\n      position: handle.position,\n      id: `${id}-${handle.id}`,\n      style: {\n        ...handleStyle,\n        ...handle.style\n      }\n    }, `${id}-${handle.id || index}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 9\n    }, this)), title && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: titleStyle,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: contentStyle,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to create handle configurations\n_c = BaseNode;\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),\n  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),\n  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),\n  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom)\n};\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Position", "theme", "jsxDEV", "_jsxDEV", "BaseNode", "id", "data", "title", "children", "handles", "style", "className", "width", "height", "nodeType", "getNodeTypeColor", "type", "colors", "nodeTypes", "gray", "defaultStyle", "background", "<PERSON><PERSON>ilter", "border", "borderRadius", "padding", "spacing", "md", "display", "flexDirection", "boxShadow", "transition", "fontFamily", "position", "overflow", "color", "titleStyle", "fontWeight", "typography", "semibold", "fontSize", "sm", "marginBottom", "xs", "textAlign", "borderBottom", "paddingBottom", "contentStyle", "flex", "justifyContent", "handleStyle", "primary", "white", "transitions", "fast", "onMouseEnter", "e", "currentTarget", "transform", "shadows", "lg", "onMouseLeave", "map", "handle", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "createHandle", "HANDLE_CONFIGS", "sourceRight", "Right", "targetLeft", "Left", "targetTop", "Top", "sourceBottom", "Bottom", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/BaseNode.js"], "sourcesContent": ["// BaseNode.js\n// Reusable base component for all node types\n\nimport { Hand<PERSON>, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\n\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 220,\n  height = 100,\n  nodeType = 'default'\n}) => {\n  const getNodeTypeColor = (type) => {\n    return theme.colors.nodeTypes[type] || theme.colors.gray[50];\n  };\n\n  const defaultStyle = {\n    width,\n    height,\n    background: `linear-gradient(135deg, rgba(140, 79, 255, 0.15) 0%, rgba(183, 112, 255, 0.1) 100%)`,\n    backdropFilter: 'blur(10px)',\n    border: `2px solid rgba(140, 79, 255, 0.3)`,\n    borderRadius: '16px',\n    padding: theme.spacing.md,\n    display: 'flex',\n    flexDirection: 'column',\n    boxShadow: `\n      0 8px 16px rgba(140, 79, 255, 0.2),\n      0 0 0 1px rgba(140, 79, 255, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.1)\n    `,\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n    position: 'relative',\n    overflow: 'hidden',\n    color: '#FFFFFF',\n    ...style\n  };\n\n  const titleStyle = {\n    fontWeight: theme.typography.fontWeight.semibold,\n    fontSize: theme.typography.fontSize.sm,\n    color: theme.colors.gray[700],\n    marginBottom: theme.spacing.xs,\n    textAlign: 'center',\n    borderBottom: `1px solid ${theme.colors.gray[200]}`,\n    paddingBottom: theme.spacing.xs,\n  };\n\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    fontSize: theme.typography.fontSize.xs,\n  };\n\n  const handleStyle = {\n    width: '12px',\n    height: '12px',\n    background: theme.colors.primary[500],\n    border: `2px solid ${theme.colors.white}`,\n    borderRadius: '50%',\n    transition: theme.transitions.fast,\n  };\n\n  return (\n    <div\n      style={defaultStyle}\n      className={`base-node ${className}`}\n      onMouseEnter={(e) => {\n        e.currentTarget.style.transform = 'translateY(-2px)';\n        e.currentTarget.style.boxShadow = theme.shadows.lg;\n      }}\n      onMouseLeave={(e) => {\n        e.currentTarget.style.transform = 'translateY(0)';\n        e.currentTarget.style.boxShadow = theme.shadows.md;\n      }}\n    >\n      {/* Render handles */}\n      {handles.map((handle, index) => (\n        <Handle\n          key={`${id}-${handle.id || index}`}\n          type={handle.type}\n          position={handle.position}\n          id={`${id}-${handle.id}`}\n          style={{\n            ...handleStyle,\n            ...handle.style\n          }}\n        />\n      ))}\n\n      {/* Title */}\n      {title && (\n        <div style={titleStyle}>\n          <span>{title}</span>\n        </div>\n      )}\n\n      {/* Content */}\n      <div style={contentStyle}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Helper function to create handle configurations\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (id = 'output') => createHandle(id, 'source', Position.Right),\n  targetLeft: (id = 'input') => createHandle(id, 'target', Position.Left),\n  targetTop: (id = 'input') => createHandle(id, 'target', Position.Top),\n  sourceBottom: (id = 'output') => createHandle(id, 'source', Position.Bottom),\n};\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,WAAW;AAC5C,SAASC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EACvBC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACRC,OAAO,GAAG,EAAE;EACZC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAIC,IAAI,IAAK;IACjC,OAAOf,KAAK,CAACgB,MAAM,CAACC,SAAS,CAACF,IAAI,CAAC,IAAIf,KAAK,CAACgB,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC;EAC9D,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBR,KAAK;IACLC,MAAM;IACNQ,UAAU,EAAG,qFAAoF;IACjGC,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAG,mCAAkC;IAC3CC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAExB,KAAK,CAACyB,OAAO,CAACC,EAAE;IACzBC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAG;AAChB;AACA;AACA;AACA,KAAK;IACDC,UAAU,EAAE,wCAAwC;IACpDC,UAAU,EAAE,2CAA2C;IACvDC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,SAAS;IAChB,GAAGzB;EACL,CAAC;EAED,MAAM0B,UAAU,GAAG;IACjBC,UAAU,EAAEpC,KAAK,CAACqC,UAAU,CAACD,UAAU,CAACE,QAAQ;IAChDC,QAAQ,EAAEvC,KAAK,CAACqC,UAAU,CAACE,QAAQ,CAACC,EAAE;IACtCN,KAAK,EAAElC,KAAK,CAACgB,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC;IAC7BuB,YAAY,EAAEzC,KAAK,CAACyB,OAAO,CAACiB,EAAE;IAC9BC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAG,aAAY5C,KAAK,CAACgB,MAAM,CAACE,IAAI,CAAC,GAAG,CAAE,EAAC;IACnD2B,aAAa,EAAE7C,KAAK,CAACyB,OAAO,CAACiB;EAC/B,CAAC;EAED,MAAMI,YAAY,GAAG;IACnBC,IAAI,EAAE,CAAC;IACPpB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBoB,cAAc,EAAE,QAAQ;IACxBT,QAAQ,EAAEvC,KAAK,CAACqC,UAAU,CAACE,QAAQ,CAACG;EACtC,CAAC;EAED,MAAMO,WAAW,GAAG;IAClBtC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdQ,UAAU,EAAEpB,KAAK,CAACgB,MAAM,CAACkC,OAAO,CAAC,GAAG,CAAC;IACrC5B,MAAM,EAAG,aAAYtB,KAAK,CAACgB,MAAM,CAACmC,KAAM,EAAC;IACzC5B,YAAY,EAAE,KAAK;IACnBO,UAAU,EAAE9B,KAAK,CAACoD,WAAW,CAACC;EAChC,CAAC;EAED,oBACEnD,OAAA;IACEO,KAAK,EAAEU,YAAa;IACpBT,SAAS,EAAG,aAAYA,SAAU,EAAE;IACpC4C,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAAC/C,KAAK,CAACgD,SAAS,GAAG,kBAAkB;MACpDF,CAAC,CAACC,aAAa,CAAC/C,KAAK,CAACoB,SAAS,GAAG7B,KAAK,CAAC0D,OAAO,CAACC,EAAE;IACpD,CAAE;IACFC,YAAY,EAAGL,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAAC/C,KAAK,CAACgD,SAAS,GAAG,eAAe;MACjDF,CAAC,CAACC,aAAa,CAAC/C,KAAK,CAACoB,SAAS,GAAG7B,KAAK,CAAC0D,OAAO,CAAChC,EAAE;IACpD,CAAE;IAAAnB,QAAA,GAGDC,OAAO,CAACqD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzB7D,OAAA,CAACJ,MAAM;MAELiB,IAAI,EAAE+C,MAAM,CAAC/C,IAAK;MAClBiB,QAAQ,EAAE8B,MAAM,CAAC9B,QAAS;MAC1B5B,EAAE,EAAG,GAAEA,EAAG,IAAG0D,MAAM,CAAC1D,EAAG,EAAE;MACzBK,KAAK,EAAE;QACL,GAAGwC,WAAW;QACd,GAAGa,MAAM,CAACrD;MACZ;IAAE,GAPI,GAAEL,EAAG,IAAG0D,MAAM,CAAC1D,EAAE,IAAI2D,KAAM,EAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQnC,CACF,CAAC,EAGD7D,KAAK,iBACJJ,OAAA;MAAKO,KAAK,EAAE0B,UAAW;MAAA5B,QAAA,eACrBL,OAAA;QAAAK,QAAA,EAAOD;MAAK;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDjE,OAAA;MAAKO,KAAK,EAAEqC,YAAa;MAAAvC,QAAA,EACtBA;IAAQ;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAC,EAAA,GA5GajE,QAAQ;AA6GrB,OAAO,MAAMkE,YAAY,GAAGA,CAACjE,EAAE,EAAEW,IAAI,EAAEiB,QAAQ,EAAEvB,KAAK,GAAG,CAAC,CAAC,MAAM;EAC/DL,EAAE;EACFW,IAAI;EACJiB,QAAQ;EACRvB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAM6D,cAAc,GAAG;EAC5BC,WAAW,EAAEA,CAACnE,EAAE,GAAG,QAAQ,KAAKiE,YAAY,CAACjE,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAACyE,KAAK,CAAC;EAC1EC,UAAU,EAAEA,CAACrE,EAAE,GAAG,OAAO,KAAKiE,YAAY,CAACjE,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAAC2E,IAAI,CAAC;EACvEC,SAAS,EAAEA,CAACvE,EAAE,GAAG,OAAO,KAAKiE,YAAY,CAACjE,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAAC6E,GAAG,CAAC;EACrEC,YAAY,EAAEA,CAACzE,EAAE,GAAG,QAAQ,KAAKiE,YAAY,CAACjE,EAAE,EAAE,QAAQ,EAAEL,QAAQ,CAAC+E,MAAM;AAC7E,CAAC;AAAC,IAAAV,EAAA;AAAAW,YAAA,CAAAX,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}